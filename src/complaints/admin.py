import logging

from decimal import Decimal
from typing import Union

from django import forms
from django.contrib import (
    admin,
    messages,
)
from django.contrib.admin.widgets import AdminFileWidget
from django.db import models
from django.http import HttpResponseRedirect
from django.urls import reverse
from django.utils import timezone
from django.utils.html import (
    format_html,
    format_html_join,
)
from django.utils.safestring import mark_safe

from rangefilter.filters import DateRangeFilter

from complaints.admin_actions import (
    change_date_to_specific_date,
    change_status_to_specific_one,
    confirm_complaints_created_by_assembly_team,
    create_production_order,
    delete_complaints_created_by_assembly_team,
    export_frontview_and_packaging_instruction_as_zip,
    export_frontviews_as_zip,
    generate_complaint_report,
    generate_complaints_costs_report,
    generate_free_return_prevention_report,
    process_complaints_to_production,
)
from complaints.admin_filters import (
    ComplaintResponsibilityByNameFilter,
    ComplaintTypicalIssuesByNameFilter,
    DeletedComplaintFilter,
    DiscountValueFilter,
    MultipleChoiceProductionComplaintReproductionElementsFilter,
    SerializedLogisticOrderDeliveredDateRangeJSONFilter,
    SerializedLogisticOrderSentToCustomerRangeJSONFilter,
)
from complaints.admin_mixins import (
    ComplaintCostDisplayAdminMixin,
    ComplaintWithPreloadedRatesAdminMixin,
)
from complaints.enums import (
    ComplaintPriorityChoices,
    ComplaintStatus,
    DamageFormStatus,
    ImageType,
)
from complaints.exceptions import CannotAbortComplaintException
from complaints.forms import DamageDataForm
from complaints.internal_api.events import (
    ComplaintAssemblyTeamInterventionRequiredEvent,
)
from complaints.models import (
    AllComplaintCosts,
    Area,
    AssemblyTeamComplaintPhoto,
    Complaint,
    ComplaintCosts,
    ComplaintHistory,
    ComplaintImage,
    ComplaintPhoto,
    CustomerContact,
    DamageData,
    DamageImage,
    LogisticComplaint,
    ProductionComplaint,
    ReplaceableShelfFittingsStock,
    Responsibility,
    SottyDamagePhoto,
    TNTCasesComplaint,
    TypicalIssues,
)
from complaints.services.complaint_photos import (
    match_complaint_with_photo_base_on_orders,
)
from complaints.services.post_save_complaints_service import (
    PostCreateComplaintService,
    send_email_to_gala_about_complaint_creation,
)
from complaints.tasks import (
    notify_logistic_about_damage_form_ready,
    send_mail_with_complaint_photos_task,
)
from custom.enums import (
    Furniture,
    PhysicalProductVersion,
)
from custom.utils.slack import (
    notify_about_accepted_complaint,
    notify_about_rejected_complaint,
)
from dixa.services.save_complaint_photos import get_matching_orders
from loose_ends.admin import (
    HasLooseEndsInlineMixin,
    LooseEndsInline,
)
from producers.admin_filters import ComplaintShelfTypeFilter

logger = logging.getLogger('cstm')


class ComplaintPhotosMixin:
    @classmethod
    def dixa_photos(cls, obj):
        photos = obj.photos.all()
        return format_html_join(
            ', ',
            '<a target="_blank" href="{}">{}</a><br/>',
            (
                (
                    reverse('admin:complaints_complaintphoto_change', args=(photo.pk,)),
                    photo.id,
                )
                for photo in photos
            ),
        )

    @admin.action(description='Send mail with complaint photos')
    def send_mail_with_complaint_photos(self, request, queryset):
        send_mail_with_complaint_photos_task.delay(
            complaint_ids=list(queryset.values_list('id', flat=True)),
            email=request.user.email,
        )
        complaints_without_photo = queryset.filter(photos__isnull=True).values_list(
            'id', flat=True
        )
        self.message_user(
            request,
            f'Email will be send soon. Complaints: {list(complaints_without_photo)} '
            'does not have photos',
        )


class ComplaintHistoryAdmin(admin.ModelAdmin):
    list_display = ('id', 'complaint', 'created_at')
    actions = ()
    raw_id_fields = ('owner',)


class ComplaintCostsForm(forms.ModelForm):
    class Meta:
        model = ComplaintCosts
        fields = (
            'refund_amount',
            'currency',
            'refund_in_euro',
            'total_cost',
            'product_cost',
            'complaint_cost',
        )


class SottyDamagePhotoInline(admin.TabularInline):
    model = SottyDamagePhoto


class ComplaintAdmin(HasLooseEndsInlineMixin, admin.ModelAdmin):
    change_form_template = 'admin/complaint_change_form.html'
    search_fields = ('product__id', 'product__order__id')
    inlines = [LooseEndsInline, SottyDamagePhotoInline]
    list_display = (
        'id',
        'get_links',
        'status',
        'product',
        'get_order_id',
        'is_priority',
        'express_replacement',
        'manufactor_fault',
        'complaint_type',
        'typical_issues',
        'responsibility',
        'total_cost',
        'get_elements_description',
        'reproduction_element_categories',
        'created_at',
        'reported_date',
        'production_ordered_date',
        'production_released_date',
        'shipment_date',
        'delivered_date',
        'logistic_dimensions_info',
        'logistic_weight',
        'material',
        'reproduction',
        'refund',
        'free_return_prevention',
        'assembly_team_intervention',
        'fittings_only',
        'additional_info',
        'logistic_additional_info',
        'production_additional_info',
        'conversation_link',
        'is_confirmed',
        'created_by_assembly_team',
        'get_assembly_photos',
    )
    actions = [
        export_frontviews_as_zip,
        process_complaints_to_production,
        generate_complaint_report,
        confirm_complaints_created_by_assembly_team,
        delete_complaints_created_by_assembly_team,
        generate_free_return_prevention_report,
        'accept_complaint',
        'reject_complaint',
        'notify_gala_about_complaint',
    ]
    date_hierarchy = 'reported_date'
    raw_id_fields = (
        'owner',
        'product',
        'reproduction_product',
        'reporter',
        'complaint_costs',
    )
    list_filter = (
        'status',
        'complaint_type',
        ComplaintResponsibilityByNameFilter,
        'express_replacement',
        'reproduction',
        MultipleChoiceProductionComplaintReproductionElementsFilter,
        'refund',
        'assembly_team_intervention',
        'production_ordered_date',
        'production_released_date',
        'shipment_date',
        'delivered_date',
        'refund_reason',
        'free_return_prevention',
        'created_by_assembly_team',
        'is_confirmed',
    )

    readonly_fields = (
        'created_at',
        'updated_at',
        'owner',
        'elements',
        'get_elements_description',
        'reproduction_element_categories',
        'deleted',
        'area',
        'complaint_type',
        'exported_to_big_query',
    )

    fieldsets = (
        (
            'Overall',
            {
                'fields': (
                    (
                        'status',
                        'express_replacement',
                    ),
                    'owner',
                    'reporter',
                    'product',
                    'reproduction_product',
                    'reported_date',
                    (
                        'complaint_type',
                        'typical_issues',
                        'refund_reason',
                        'area',
                        'responsibility',
                    ),
                    'complaint_costs',
                    'manufactor_fault',
                    'get_elements_description',
                    'additional_info',
                    'conversation_link',
                    (
                        'reproduction',
                        'refund',
                        'free_return_prevention',
                        'assembly_team_intervention',
                        'fittings_only',
                        'is_repeated',
                        'boxes_damaged',
                    ),
                    'deleted',
                    'exported_to_big_query',
                ),
            },
        ),
        (
            'Production',
            {
                'fields': (
                    'production_ordered_date',
                    'material',
                    'production_additional_info',
                ),
            },
        ),
        (
            'Logistic',
            {
                'fields': (
                    'production_released_date',
                    'shipment_date',
                    'delivered_date',
                    'logistic_dimensions_info',
                    'logistic_weight',
                    'logistic_additional_info',
                ),
            },
        ),
        (
            'TNT case',
            {
                'classes': ('collapse',),
                'fields': (
                    'tnt_complaint_type',
                    'tnt_order_number',
                    'tnt_tracking_number',
                    'tnt_complaint_date',
                    'tnt_notification_channel',
                    'tnt_notification_by_customer',
                    'tnt_additional_info',
                    'tnt_status',
                    'tnt_final_result',
                ),
            },
        ),
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.preloaded_pricing = None

    def validate_is_only_sotty(self, request, queryset):
        if queryset.exclude(
            product__cached_product_type=Furniture.sotty.value
        ).exists():
            messages.error(request, 'Action only for sotty')
            return False
        return True

    @admin.action(description='Accept sofa complaint')
    def accept_complaint(self, request, queryset):
        if not self.validate_is_only_sotty(request, queryset):
            return
        for complaint in queryset:
            complaint.status = ComplaintStatus.NEW
            complaint.save(update_fields=['status'])
            post_create_actions = PostCreateComplaintService(
                request,
                complaint,
                'Complaints were accepted',
                elements={},
                refund=complaint.refund,
                refund_amount=complaint.complaint_costs.refund_amount,
                refund_reason=complaint.refund_reason,
            )
            post_create_actions.run()
            notify_about_accepted_complaint(complaint.id, complaint.conversation_link)

    @admin.action(description='Reject sofa complaint')
    def reject_complaint(self, request, queryset):
        if not self.validate_is_only_sotty(request, queryset):
            return
        for complaint in queryset:
            complaint.status = ComplaintStatus.REJECTED.value
            complaint.save(update_fields=['status'])
            notify_about_rejected_complaint(complaint.id, complaint.conversation_link)

    @admin.action(description='Notify Gala about new complaint')
    def notify_gala_about_complaint(self, request, queryset):
        if not self.validate_is_only_sotty(request, queryset):
            return
        for complaint in queryset:
            send_email_to_gala_about_complaint_creation(complaint)

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    @admin.display(boolean=True)
    def is_priority(self, obj):
        return obj.priority == ComplaintPriorityChoices.PRIORITY

    def delete_model(self, request, obj):
        if not obj:
            return super().delete_model(request, obj)
        if obj.reproduction_product:
            obj.reproduction_product.delete()
        return super().delete_model(request, obj)

    def save_model(self, request, obj, form, change):
        if 'status' in form.changed_data:
            status_previous = form.initial['status']
            status_new = form.cleaned_data['status']
            try:
                obj.change_status(
                    status_new=status_new,
                    status_previous=status_previous,
                )
            except CannotAbortComplaintException as exc:
                messages.add_message(request, level=messages.WARNING, message=exc)
                messages.set_level(request, messages.ERROR)
                return HttpResponseRedirect(request.get_full_path())

        if 'responsibility' in form.changed_data:
            if (
                form.cleaned_data['responsibility']
                and form.cleaned_data['responsibility'].name
                in Responsibility.MANUFACTOR_FAULT_RESPONSIBILITIES
            ):
                obj.manufactor_fault = True
            else:
                obj.manufactor_fault = False

        if (
            'assembly_team_intervention' in form.changed_data
            and not form.initial['assembly_team_intervention']
            and form.cleaned_data['assembly_team_intervention']
        ):
            reproduction_order = obj.reproduction_order
            if obj.reproduction and reproduction_order:
                reproduction_order.set_order_notes('Assembly team intervention')
                reproduction_logistic_order = (
                    reproduction_order.logistic_info[0].id
                    if reproduction_order.logistic_info
                    else None
                )
                ComplaintAssemblyTeamInterventionRequiredEvent(
                    obj, reproduction_logistic_order
                )
            elif not obj.reproduction:
                ComplaintAssemblyTeamInterventionRequiredEvent(
                    complaint=obj, reproduction_logistic_order_id=None
                )
            else:
                messages.add_message(
                    request,
                    messages.WARNING,
                    'Cannot set note "Assembly team intervention" '
                    'and create complaint service '
                    'because Complaint is not processed to production yet '
                    'so it hasn"t got reproduction_order created',
                )
        super().save_model(request, obj, form, change)

    def shelf_type(self, obj):
        return obj.product.cached_shelf_type

    def get_queryset(self, request):
        """
        This method load queryset and preload pricing with manufacturers ids
        from queryset.
        """
        queryset = (
            super()
            .get_queryset(request)
            .select_related(
                'typical_issues',
                'complaint_type',
                'complaint_costs',
                'area',
                'responsibility',
                'product',
                'product__manufactor',
                'product__order',
                'reproduction_product',
                'reproduction_product__order',
                'reproduction_product__manufactor',
                'reproduction_product__product_details_jetty',
                'reproduction_product__product_details_watty',
            )
            .prefetch_related(
                'product__order_item__order_item',
            )
        )

        return queryset

    @mark_safe
    @admin.display(description='Actions')
    def get_links(self, obj):
        owner_id = obj.product.order.owner_id
        link_to_summary = f'<a href="/cs/user_overview/{owner_id}/"> User overview </a>'
        if obj.reproduction_product:
            link_to_summary += '<br/><br/>'
            link_to_summary += (
                f'<a href="/admin/producers/productcomplaint'
                f'/{obj.reproduction_product_id}/"> '
                f'Reproduction Product: {obj.reproduction_product_id}</a>'
            )
        return link_to_summary

    @admin.display(description='COGS (€)')
    def get_complaint_cost(self, obj):
        return obj.complaint_costs.complaint_cost

    def total_cost(self, obj):
        return obj.complaint_costs.total_cost

    @admin.display(description='Assembly Photos')
    def get_assembly_photos(self, obj):
        # get AssemblyTeamComplaintPhoto objects thumbnails by photo field in img tag
        photos = AssemblyTeamComplaintPhoto.objects.filter(complaint=obj)
        return format_html_join(
            ', ',
            '<img src="{}" width="300px" />',
            ((photo.photo.url,) for photo in photos),
        )


class LogisticComplaintAdmin(ComplaintAdmin):
    list_filter = (
        'status',
        'express_replacement',
        'production_ordered_date',
        'shipment_date',
        'delivered_date',
        'tnt_complaint_type',
        'tnt_status',
        ComplaintResponsibilityByNameFilter,
        'complaint_type',
        'reproduction',
        'refund',
        'assembly_team_intervention',
        'fittings_only',
        DeletedComplaintFilter,
    )

    list_display = (
        'id',
        'status',
        'complaints_count',
        'is_priority',
        'express_replacement',
        'assembly_team_intervention',
        'logistic_additional_info',
        'fittings_only',
        'product',
        'shelf_type',
        'get_order_id',
        'production_ordered_date',
        'production_released_date',
        'shipment_date',
        'delivered_date',
        'logistic_dimensions_info',
        'conversation_link_display',
        'logistic_weight',
        'complaint_type',
        'typical_issues',
        'area',
        'responsibility',
        'get_elements_description',
        'created_at',
        'reported_date',
        'material',
        'reproduction',
        'is_repeated',
        'boxes_damaged',
        'refund',
        'additional_info',
        'production_additional_info',
    )

    actions = [
        export_frontview_and_packaging_instruction_as_zip,
        process_complaints_to_production,
        generate_complaint_report,
    ]

    def has_change_permission(self, request, obj=None):
        return True

    def save_model(self, request, obj, form, change):
        if (
            change
            and 'shipment_date' in form.changed_data
            and not Complaint.objects.get(pk=obj.pk).shipment_date
        ):
            obj.status = 'done'
            obj.save(
                update_fields=[
                    'status',
                ]
            )
        super(LogisticComplaintAdmin, self).save_model(request, obj, form, change)

    @staticmethod
    @admin.display(description='Conversation link')
    def conversation_link_display(obj):
        return format_html(
            f'<a href="{obj.conversation_link}" target="_blank" '
            f'rel="noopener noreferrer">'
            f'{obj.conversation_link}</a>'
        )


class ComplaintHistoryInline(admin.TabularInline):
    model = ComplaintHistory
    ordering = ('created_at',)
    readonly_fields = ('owner',)
    can_delete = False
    extra = 0


class ProductionComplaintAdmin(
    ComplaintWithPreloadedRatesAdminMixin, ComplaintPhotosMixin, ComplaintAdmin
):
    list_display = (
        'id',
        'status',
        'product',
        'shelf_type',
        'admin_get_physical_product_version',
        'get_order_id',
        'is_priority',
        'express_replacement',
        'manufactor_fault',
        'complaint_type',
        'typical_issues',
        'area',
        'responsibility',
        'damage_data_links',
        'dixa_photos',
        'carrier',
        'tracking_number',
        'shelf_id',
        'delivery_date',
        'reproduction_logistic_cost',
        'get_elements_description',
        'reproduction_element_categories',
        'material',
        'reproduction',
        'is_repeated',
        'boxes_damaged',
        'created_at',
        'reported_date',
        'production_ordered_date',
        'production_released_date',
        'cs_request_date',
        'shipment_date',
        'delivered_date',
        'refund',
        'refund_reason',
        'assembly_team_intervention',
        'additional_info',
        'logistic_additional_info',
        'production_additional_info',
        'conversation_link_display',
    )

    list_filter = (
        'product__manufactor',
        'product__cached_physical_product_version',
        ComplaintShelfTypeFilter,
        'express_replacement',
        'reproduction',
        MultipleChoiceProductionComplaintReproductionElementsFilter,
        'is_repeated',
        'refund',
        'refund_reason',
        'assembly_team_intervention',
        'boxes_damaged',
        'fittings_only',
        'status',
        ComplaintTypicalIssuesByNameFilter,
        ComplaintResponsibilityByNameFilter,
    )

    actions = (
        export_frontviews_as_zip,
        process_complaints_to_production,
        generate_complaint_report,
        change_date_to_specific_date,
        change_status_to_specific_one,
        create_production_order,
        'update_complaint_cost',
        'send_mail_with_complaint_photos',
    )
    readonly_fields = (
        'created_at',
        'updated_at',
        'owner',
        'elements',
        'get_elements_description',
        'reproduction_element_categories',
        'deleted',
        'area',
        'complaint_type',
        'damage_data_links',
        'carrier',
        'tracking_number',
        'shelf_id',
        'delivery_date',
    )
    fieldsets = (
        (
            'Overall',
            {
                'fields': (
                    (
                        'status',
                        'express_replacement',
                    ),
                    'owner',
                    'reporter',
                    'product',
                    'reproduction_product',
                    'reported_date',
                    ('complaint_type', 'typical_issues', 'area', 'responsibility'),
                    'damage_data_links',
                    (
                        'carrier',
                        'tracking_number',
                        'shelf_id',
                        'delivery_date',
                    ),
                    'manufactor_fault',
                    'get_elements_description',
                    'additional_info',
                    'conversation_link',
                    (
                        'reproduction',
                        'refund',
                        'assembly_team_intervention',
                        'fittings_only',
                        'is_repeated',
                        'boxes_damaged',
                    ),
                    'deleted',
                ),
            },
        ),
        (
            'Production',
            {
                'fields': (
                    'production_ordered_date',
                    'material',
                    'production_additional_info',
                ),
            },
        ),
        (
            'Logistic',
            {
                'fields': (
                    'production_released_date',
                    'shipment_date',
                    'delivered_date',
                    'logistic_dimensions_info',
                    'logistic_weight',
                    'logistic_additional_info',
                ),
            },
        ),
        (
            'TNT case',
            {
                'classes': ('collapse',),
                'fields': (
                    'tnt_complaint_type',
                    'tnt_order_number',
                    'tnt_tracking_number',
                    'tnt_complaint_date',
                    'tnt_notification_channel',
                    'tnt_notification_by_customer',
                    'tnt_additional_info',
                    'tnt_status',
                    'tnt_final_result',
                ),
            },
        ),
    )
    inlines = (ComplaintHistoryInline,)

    def has_change_permission(self, request, obj=None):
        return True

    def damage_data_links(self, obj):
        damage_data_list = obj.product.damagedata_set.all()

        return format_html_join(
            '',
            '<a target="_blank" href="{}">{}</a><br/>',
            (
                (
                    reverse(
                        'admin:complaints_damagedata_change', args=(damage_data.pk,)
                    ),
                    str(damage_data),
                )
                for damage_data in damage_data_list
            ),
        )

    def carrier(self, obj):
        return obj.product.get_carrier_name()

    def tracking_number(self, obj):
        return obj.product.get_tracking_number()

    def shelf_id(self, obj):
        return obj.product.pk

    def delivery_date(self, obj):
        return obj.product.get_delivered_date()

    def get_queryset(self, request):
        self.load_current_rates()

        queryset = super().get_queryset(request)
        return (
            queryset.select_related(
                'reproduction_order',
                'reproduction_order__region',
                'product__order_item',
                'product__order_item__region',
            )
            .prefetch_related(
                'reproduction_order__region__countries',
                'product__order_item__region__countries',
                'photos',
            )
            .with_logistic_costs_for_fittings()
            .with_fittings_only_check()
        )

    @admin.action(description='Update complaint cost')
    def update_complaint_cost(self, request, queryset):
        if queryset.count() > 100:
            self.message_user(
                request, 'To many objects, choose less than 100 complaints'
            )
            return
        for complaint in queryset:
            complaint.update_complaint_cost()

    @admin.display(description='ProdVersion')
    def admin_get_physical_product_version(self, obj):
        return PhysicalProductVersion.get_name(
            obj.product.cached_physical_product_version
        )

    @staticmethod
    @admin.display(description='Conversation link')
    def conversation_link_display(obj):
        return format_html(
            f'<a href="{obj.conversation_link}" '
            f'target="_blank" rel="noopener noreferrer">'
            f'{obj.conversation_link}</a>'
        )

    @admin.display(description='LOGS (€)')
    def reproduction_logistic_cost(self, obj) -> Union[Decimal, str]:
        return self.get_reproduction_logistic_cost(obj)


class AllComplaintCostsAdmin(
    ComplaintCostDisplayAdminMixin, ComplaintPhotosMixin, ComplaintAdmin
):
    list_display = (
        'id',
        'status',
        'product',
        'get_reproduction_product',
        'shelf_type',
        'admin_get_physical_product_version',
        'get_order_id',
        'complaint_type',
        'typical_issues',
        'get_dedicated_transport_prediction',
        'get_tnt_prediction',
        'get_ups_prediction',
        'get_dpd_prediction',
        'area',
        'responsibility',
        'boxes_damaged',
        'dixa_photos',
        'get_elements_description',
        'reproduction_element_categories',
        'get_reproduction_elements_count',
        'get_product_to_be_shipped_date',
        'get_product_delivered_date',
        'get_reproduction_product_delivered_date',
        'created_at',
        'get_carrier',
        'get_complaint_carrier',
        'refund_reason',
        'get_free_refund_reason_tag',
        'get_complaint_cost',
        'reproduction_logistic_cost',
        'get_as_team_intervention_value',
        'get_refund_in_euro',
        'get_cogs_product_cost',
        'get_logistic_order_cost',
        'get_logs_free_return_shipping_costs',
        'get_packaging_kit_cogs',
        'get_frp_cost',
        'total_costs',
    )

    list_filter = (
        'status',
        'product__manufactor',
        'product__cached_physical_product_version',
        ComplaintShelfTypeFilter,
        MultipleChoiceProductionComplaintReproductionElementsFilter,
        'is_repeated',
        ComplaintResponsibilityByNameFilter,
        ComplaintTypicalIssuesByNameFilter,
        DiscountValueFilter,
        ('product__product_status_history__changed_at', DateRangeFilter),
        (
            'product__order__serialized_logistic_info',
            SerializedLogisticOrderSentToCustomerRangeJSONFilter,
        ),
        (
            'product__order__serialized_logistic_info',
            SerializedLogisticOrderDeliveredDateRangeJSONFilter,
        ),
        ('created_at', DateRangeFilter),
        'refund_reason',
        'free_return_prevention',
        'product__order__region__name',
    )

    actions = [
        generate_complaints_costs_report,
        'send_mail_with_complaint_photos',
    ]

    inlines = (ComplaintHistoryInline,)

    def get_queryset(self, request):
        self.load_current_rates()
        queryset = super().get_queryset(request)
        queryset = queryset.prefetch_related('photos')
        return queryset

    def get_rangefilter_product__product_status_history__changed_at_title(
        self, request, field_path
    ):
        return 'To be shipped date'

    def get_rangefilter_product__order__serialized_logistic_info__0__sent_to_customer_title(  # noqa E501
        self, request, field_path
    ):
        return 'Sent to Customer'

    def get_rangefilter_product__order__serialized_logistic_info__0__delivered_date_title(  # noqa E501
        self, request, field_path
    ):
        return 'Delivered at'


class TNTCasesComplaintAdmin(ComplaintAdmin):
    list_filter = (
        'status',
        'tnt_complaint_type',
        'tnt_status',
        ComplaintResponsibilityByNameFilter,
        'complaint_type',
        'reproduction',
        'refund',
        'assembly_team_intervention',
    )
    list_display = (
        'id',
        'status',
        'tnt_status',
        'tnt_final_result',
        'get_order_id',
        'tnt_complaint_type',
        'tnt_complaint_date',
        'tnt_notification_channel',
        'responsibility',
        'complaint_type',
        'get_elements_description',
        'material',
        'reproduction',
        'created_at',
        'reported_date',
        'production_ordered_date',
        'product',
        'production_released_date',
        'cs_request_date',
        'shipment_date',
        'delivered_date',
        'refund',
        'assembly_team_intervention',
        'additional_info',
        'logistic_additional_info',
        'production_additional_info',
        'conversation_link',
    )


class ContactImageItemsInline(admin.TabularInline):
    model = ComplaintImage
    verbose_name = 'Damaged elements image'
    verbose_name_plural = 'Damaged elements images'
    readonly_fields = ('download_image_link',)
    fields = ('image', 'image_type', 'download_image_link')
    image_type = ImageType.DAMAGED_ELEMENTS

    def get_queryset(self, request):
        qs = super(ContactImageItemsInline, self).get_queryset(request)
        return qs.filter(image_type=self.image_type.value)

    def download_image_link(self, obj):
        html = '<a download="{}" href="{}" title="ImageName">' 'Download' '</a>'
        return format_html(html, obj.image.name, obj.image.url)


class ContactImagePackageInline(ContactImageItemsInline):
    verbose_name = 'Damaged packege image'
    verbose_name_plural = 'Damaged package images'
    image_type = ImageType.DAMAGED_PACKAGE


class CustomerContactAdmin(admin.ModelAdmin):
    inlines = (ContactImageItemsInline, ContactImagePackageInline)
    raw_id_fields = ('owner',)
    list_filter = ('topic',)
    list_display = (
        'id',
        'email',
        'order',
        'topic',
        'has_missing_elements',
        'missing_elements_description',
        'has_damaged_shelf_elements',
        'has_damaged_packaging',
        'is_reported_to_delivery_company',
        'created',
        'damages_description',
        'thumbnails',
    )

    @mark_safe
    def thumbnails(self, obj):
        if obj.images.count() == 0:
            return ''
        response = ''
        for complaintImage in obj.images.all():
            response += '<img src="{}" width="300px" />'.format(
                complaintImage.image.url,
            )
        return '<div style="width:300px; float:left">{}</div>'.format(response)

    thumbnails.short_description = 'Thumbnail'


class ReplaceableShelfFittingsStockAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'code',
        'name_pl',
        'name_en',
        'fitting_image',
        'side',
        'quantity',
    )
    list_filter = ('code', 'side')
    search_fields = ('name_pl', 'name_en')

    def fitting_image(self, obj):
        return mark_safe(
            '<img src="{url}" height={height} />'.format(
                url=obj.picture.url if obj.picture else None,
                height='150px',
            )
        )


class AreaChoiceField(forms.ModelChoiceField):
    def label_from_instance(self, obj):
        return 'Name: %s Typical issues: %s' % (obj.name, obj.typical_issue)


class ResponsibilityAdminForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['area'] = AreaChoiceField(
            queryset=Area.objects.filter(is_deprecated=False)
        )

    class Meta:
        model = Responsibility
        fields = '__all__'


class ResponsibilityAdmin(admin.ModelAdmin):
    form = ResponsibilityAdminForm
    list_display = ('id', 'name', 'is_deprecated')
    list_filter = ('is_deprecated',)


class AdminImageWidget(AdminFileWidget):
    def render(self, name, value, attrs=None, renderer=None):
        output = []

        if value and getattr(value, 'url', None):
            image_url = value.url
            file_name = str(value)

            output.append(
                f'<a href="{image_url}" target="_blank">'
                f'<img src="{image_url}" alt="{file_name}" width="150" height="150" '
                f'style="object-fit: cover;"/> </a>'
            )

        output.append(super(AdminFileWidget, self).render(name, value, attrs, renderer))
        return mark_safe(''.join(output))


class DamagedPackageImageInline(admin.TabularInline):
    model = DamageImage
    formfield_overrides = {models.ImageField: {'widget': AdminImageWidget}}

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.filter(image_type=ImageType.DAMAGED_PACKAGE.value)


class DamagedElementsImageInline(admin.TabularInline):
    model = DamageImage
    formfield_overrides = {models.ImageField: {'widget': AdminImageWidget}}

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.filter(image_type=ImageType.DAMAGED_ELEMENTS.value)


class DamageDataAdmin(admin.ModelAdmin):
    list_display = ('product_id', 'status')
    list_filter = ('status',)
    inlines = [
        DamagedPackageImageInline,
        DamagedElementsImageInline,
    ]
    readonly_fields = ('product',)
    form = DamageDataForm

    def save_model(self, request, obj, form, change):
        super().save_model(request, obj, form, change)

        is_state_changed = (
            change and 'status' in form.changed_data and form.cleaned_data.get('status')
        )
        if is_state_changed:
            obj.ready_since = timezone.now()
            obj.save(update_fields=['ready_since'])
            if form.cleaned_data.get('status') == DamageFormStatus.READY:
                notify_logistic_about_damage_form_ready.delay(
                    obj.id, obj.product.order.id
                )


class ComplaintCostsAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'currency',
        'refund_amount',
        'refund_in_euro',
        'total_cost',
        'product_cost',
        'complaint_cost',
    )


class ComplaintPhotoAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'file_name',
        'complaint_id',
        'get_orders',
        'dixa_conversation_link',
        'created_at',
        'email',
    )
    raw_id_fields = ('orders', 'complaint')
    search_fields = ('file_name', 'dixa_conversation_id')
    actions = ('match_photos_to_complaints', 'refresh_matching_orders')

    def get_queryset(self, request):
        return super().get_queryset(request).prefetch_related('orders')

    @classmethod
    @admin.display(description='Orders')
    def get_orders(cls, obj):
        return ', '.join([str(order.id) for order in obj.orders.all()])

    @classmethod
    def dixa_conversation_link(cls, obj):
        if not obj.dixa_conversation_id:
            return '-'
        return format_html(
            f'<a href="https://tylko.dixa.com/conversation/{obj.dixa_conversation_id}"'
            f'target="_blank">'
            f'{obj.dixa_conversation_id}</a>'
        )

    def match_photos_to_complaints(self, request, queryset):
        without_orders = []
        with_to_many_complaints = []
        without_complaints = []
        with_assign_complaint = []

        for complaint_photo in queryset:
            if complaint_photo.complaint:
                with_assign_complaint.append(complaint_photo.id)
                continue
            if not complaint_photo.orders.exists():
                without_orders.append(complaint_photo.id)
                continue
            try:
                match_complaint_with_photo_base_on_orders(complaint_photo)
            except Complaint.DoesNotExist:
                without_complaints.append(complaint_photo.id)
            except Complaint.MultipleObjectsReturned:
                with_to_many_complaints.append(complaint_photo.id)
        if without_orders:
            self.message_user(request, f'Without orders: {without_orders}')
        if without_complaints:
            self.message_user(request, f'Without complaints: {without_complaints}')
        if with_to_many_complaints:
            self.message_user(request, f'To many complaints: {with_to_many_complaints}')
        if with_assign_complaint:
            self.message_user(
                request, f'With assign complaint: {with_assign_complaint}'
            )

    @admin.display(description='Refresh matching orders')
    def refresh_matching_orders(self, request, queryset):
        for complaint_photo in queryset:
            orders = get_matching_orders(complaint_photo.email)
            if orders:
                complaint_photo.orders.set(orders)


class AssemblyTeamComplaintPhotoAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'complaint_url',
        'display_preview',
        'photo',
        'created_at',
    )

    @admin.display(description='Complaint')
    def complaint_url(self, obj):
        url = reverse('admin:complaints_complaint_change', args=(obj.complaint.id,))
        return format_html(f'<a href="{url}">{obj.complaint.id}</a>')

    @admin.display(description='Preview')
    def display_preview(self, obj):
        if obj.photo:
            return format_html(
                f'<img src="{obj.photo.url}" style="width: 200px; height: auto;" />'
            )
        return '(No image)'


class TypicalIssuesAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_sotty_issue', 'responsibility_name', 'is_deprecated')
    list_filter = ('is_deprecated', 'is_sotty_issue', 'responsibility')
    search_fields = ('name',)

    list_select_related = ['responsibility']

    def responsibility_name(self, obj):
        return obj.responsibility.name if obj.responsibility else '-'


admin.site.register(TypicalIssues, TypicalIssuesAdmin)
admin.site.register(CustomerContact, CustomerContactAdmin)
admin.site.register(ComplaintHistory, ComplaintHistoryAdmin)
admin.site.register(Complaint, ComplaintAdmin)
admin.site.register(LogisticComplaint, LogisticComplaintAdmin)
admin.site.register(ProductionComplaint, ProductionComplaintAdmin)
admin.site.register(AllComplaintCosts, AllComplaintCostsAdmin)
admin.site.register(TNTCasesComplaint, TNTCasesComplaintAdmin)
admin.site.register(ReplaceableShelfFittingsStock, ReplaceableShelfFittingsStockAdmin)
admin.site.register(Responsibility, ResponsibilityAdmin)
admin.site.register(DamageData, DamageDataAdmin)
admin.site.register(ComplaintCosts, ComplaintCostsAdmin)
admin.site.register(ComplaintPhoto, ComplaintPhotoAdmin)
admin.site.register(AssemblyTeamComplaintPhoto, AssemblyTeamComplaintPhotoAdmin)
