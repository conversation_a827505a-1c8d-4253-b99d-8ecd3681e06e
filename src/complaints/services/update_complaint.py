from datetime import datetime
from decimal import Decimal

from complaints.models import (
    Co<PERSON><PERSON><PERSON>,
    SottyDamagePhoto,
    TypicalIssues,
)
from complaints.reproduction_days_elements import get_elements_categories
from complaints.reproduction_elements import (
    get_complaint_element_dict,
    get_element_details,
)
from complaints.services.create_complaint import separate_deprecated_drawer_elements
from producers.errors import ComplaintUpdateWithDeprecatedElementsError
from producers.models import Product


def update_complaint(
    elements: dict,
    product: Product,
    additional_data: dict,
    complaint: Complain<PERSON>,
    reporter,
    reported_date: datetime = datetime.now(),
    typical_issues: TypicalIssues | None = None,
    reproduction: bool = False,
    notification_type: str = '',
    total_cost: Decimal = Decimal('0.00'),
    free_return_prevention: bool = False,
    refund: bool = False,
    assembly_team_intervention: bool = False,
    boxes_damaged: bool = False,
    refund_reason: str = '',
    additional_info: str = '',
    conversation_link: str = '',
    packages_info_was_provided: bool = False,
    is_repeated: bool = False,
    fittings_only: bool = False,
    sotty_photos: list | None = None,
) -> Complaint:

    element_details = get_element_details(elements, product)
    _, deprecated_elements = separate_deprecated_drawer_elements(
        product, element_details
    )
    if deprecated_elements:
        raise ComplaintUpdateWithDeprecatedElementsError(
            'Complaint update with deprecated elements is not allowed'
        )
    complaint_elements = get_complaint_element_dict(element_details)
    element_categories = get_elements_categories(complaint_elements)
    additional_data.update(
        {
            'elements': complaint_elements,
            'reproduction_element_categories': element_categories,
        }
    )
    sotty_photos = sotty_photos or []
    complaint.elements = complaint_elements
    complaint.reproduction_element_categories = element_categories
    complaint.notification_type = notification_type
    complaint.reporter = reporter
    complaint.reported_date = reported_date
    complaint.typical_issues = typical_issues
    complaint.responsibility = typical_issues.responsibility if typical_issues else None
    complaint.manufactor_fault = (
        complaint.responsibility.is_manufacturer_responsibility()
        if complaint.responsibility
        else False
    )
    complaint.reproduction = reproduction
    complaint.total_cost = total_cost
    complaint.free_return_prevention = free_return_prevention
    complaint.refund = refund
    complaint.assembly_team_intervention = assembly_team_intervention
    complaint.boxes_damaged = boxes_damaged
    complaint.refund_reason = refund_reason
    complaint.additional_info = additional_info
    complaint.conversation_link = conversation_link
    complaint.packages_info_was_provided = packages_info_was_provided
    complaint.is_repeated = is_repeated
    complaint.fittings_only = fittings_only

    complaint.save()
    for photo in sotty_photos:
        SottyDamagePhoto.objects.create(
            photo=photo,
            file_name=photo.name,
            complaint=complaint,
        )
    return complaint
