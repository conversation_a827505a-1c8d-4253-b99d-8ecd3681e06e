import json
import logging
import os

from collections import (
    Counter,
    defaultdict,
)
from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal
from functools import cached_property
from itertools import chain
from uuid import uuid4

from django.conf import settings
from django.contrib.postgres.fields import <PERSON><PERSON>y<PERSON>ield
from django.core.validators import FileExtensionValidator
from django.db import (
    models,
    transaction,
)
from django.utils import timezone
from django.utils.html import format_html

from rest_framework import serializers
from safedelete.models import SafeDeleteModel

from complaints import enums
from complaints.choices import NotificationTypes
from complaints.costs_calculator import (
    get_fittings_only_cost,
    get_product_cost,
    get_reproduction_cost,
)
from complaints.enums import (
    BOOL_CHOICES,
    ComplaintStatus,
    DamageFormStatus,
    ReplaceableShelfFittingsStockSide,
    TNTComplaintType,
    TNTNotificationByCustomer,
    TNTNotificationChannel,
    TNTStatus,
)
from complaints.exceptions import (
    ComplaintNotReproductionException,
    ComplaintNotReproductionProductException,
)
from complaints.internal_api.events import ComplaintRefreshEvent
from complaints.managers import (
    ActiveTagManager,
    ComplaintCostsManager,
    ComplaintManager,
)
from complaints.reproduction_days_elements import ElementFitting
from complaints.reproduction_elements import get_element_details
from complaints.services.statuses import abort_complaint
from complaints.utils import validate_image_size
from cstm_be.media_storage import private_media_storage
from custom.internal_api.dto import LogisticOrderDTO
from custom.models import (
    ExchangeRate,
    Timestampable,
)
from custom.templatetags.defaulttags import file_url
from custom.utils.exchange import EuroRateNotFound
from events.domain_events.marketing_events import MarketingExcludeUpdateEvent
from orders.enums import OrderStatus
from producers.utils import RandomizedUploadTo

logger = logging.getLogger('cstm')


@dataclass
class ComplaintElementsWithAmount:
    elements: list[str]
    amount: int


class ComplaintTagBaseModel(models.Model):
    is_deprecated = models.BooleanField(default=False)
    name = models.CharField(max_length=255)

    objects = ActiveTagManager()

    class Meta:
        abstract = True

    def __str__(self):
        return '{0}{1}'.format(self.name, '(Deprecated)' if self.is_deprecated else '')


class ComplaintType(ComplaintTagBaseModel):
    """Model for storing data about type of complaint."""


class TypicalIssues(ComplaintTagBaseModel):
    """Model for storing data about typical issues of complaint."""

    is_sotty_issue = models.BooleanField(default=False)
    responsibility = models.ForeignKey(
        'complaints.Responsibility',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
    )


class Area(ComplaintTagBaseModel):
    """Model for storing data about area of complaint."""


class Responsibility(ComplaintTagBaseModel):
    MANUFACTOR_FAULT_RESPONSIBILITIES = ('manufacturer',)

    def is_manufacturer_responsibility(self):
        return self.name.lower() in self.MANUFACTOR_FAULT_RESPONSIBILITIES


class ComplaintCosts(Timestampable):
    currency = models.CharField(max_length=3, blank=True)
    refund_amount = models.DecimalField(
        blank=True,
        null=True,
        max_digits=7,
        decimal_places=2,
        verbose_name='Refund in order currency',
    )
    refund_in_euro = models.DecimalField(
        blank=True, null=True, max_digits=7, decimal_places=2, verbose_name='Refund (€)'
    )
    total_cost = models.DecimalField(
        blank=True, null=True, max_digits=7, decimal_places=2
    )
    product_cost = models.DecimalField(
        default=0.00, max_digits=7, decimal_places=2, verbose_name='COGS Product (€)'
    )
    complaint_cost = models.DecimalField(
        default=0.00, max_digits=7, decimal_places=2, verbose_name='COGS (€)'
    )

    class Meta:
        verbose_name_plural = 'Complaint costs'

    def save(self, *args, **kwargs):
        if self.id is None:
            self.refund_in_euro = self.get_refund_in_euro()
        super().save(*args, **kwargs)

    def get_refund_in_euro(self):
        if self.refund_amount is None:
            return None

        if self.currency == 'EUR':
            return self.refund_amount

        today = timezone.now()
        exchange_rate = ExchangeRate.get_safe_exchange(
            today.year,
            today.month,
            today.day,
        )
        if not self.currency:
            return Decimal('0.00')

        if self.currency == 'PLN':
            refund_in_euro = round(
                self.refund_amount / Decimal(exchange_rate['EUR']), 2
            )
        else:
            refund_in_euro = round(
                (Decimal(exchange_rate[self.currency.upper()]) * self.refund_amount)
                / Decimal(exchange_rate['EUR']),
                2,
            )
        return refund_in_euro

    def get_complaint_cost_in_region_currency(self):
        complaint = self.complaints.first()
        currency_code = complaint.product.order.currency.code.upper()
        if currency_code == 'EUR':
            return self.complaint_cost

        today = timezone.now()
        exchange_rate = ExchangeRate.get_safe_exchange(
            today.year,
            today.month,
            today.day,
        )

        rate_euro = Decimal(exchange_rate['EUR'])
        rate_chf_or_gbp = Decimal(exchange_rate[currency_code])
        complaint_cost_in_region = self.complaint_cost * (rate_euro / rate_chf_or_gbp)
        return round(complaint_cost_in_region, 2)


class Complaint(SafeDeleteModel, models.Model):
    owner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
    )
    status = models.CharField(
        max_length=128,
        choices=enums.ComplaintStatus.choices(),
        default=enums.ComplaintStatus.NEW.value,
    )
    previous_status = models.CharField(
        max_length=128, choices=enums.ComplaintStatus.choices(), blank=True
    )
    reporter = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        related_name='reporter',
        on_delete=models.CASCADE,
    )
    product = models.ForeignKey(
        'producers.Product',
        on_delete=models.CASCADE,
    )
    reproduction_order = models.ForeignKey(
        'orders.Order',
        related_name='complaints',
        null=True,
        on_delete=models.SET_NULL,
    )
    reproduction_product = models.ForeignKey(
        'producers.Product',
        related_name='reproduction_complaints',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )
    reported_date = models.DateField(null=True, blank=True)
    cs_forwarded_transport_damage_date = models.DateField(null=True, blank=True)
    production_ordered_date = models.DateField(null=True, blank=True)
    production_released_date = models.DateField(null=True, blank=True)
    cs_request_date = models.DateField(null=True, blank=True)
    is_repeated = models.BooleanField(default=False)
    boxes_damaged = models.BooleanField(default=False)
    manufactor_fault = models.BooleanField(default=False)
    free_return_prevention = models.BooleanField(default=False)
    notification_type = models.PositiveSmallIntegerField(
        choices=NotificationTypes.choices,
        default=NotificationTypes.NO_INFO,
    )

    shipment_date = models.DateField(null=True, blank=True)
    delivered_date = models.DateField(null=True, blank=True)

    complaint_type = models.ForeignKey(
        'complaints.ComplaintType',
        null=True,
        on_delete=models.CASCADE,
    )
    typical_issues = models.ForeignKey(
        'complaints.TypicalIssues',
        null=True,
        on_delete=models.CASCADE,
    )
    area = models.ForeignKey(
        'complaints.Area',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )
    responsibility = models.ForeignKey(
        'complaints.Responsibility',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )

    packages_info_was_provided = models.BooleanField(default=False)

    elements = models.JSONField(
        default=dict, blank=True
    )  # list of elements in complaint

    logistic_dimensions_info = models.TextField(null=True, blank=True)
    logistic_weight = models.FloatField(null=True, blank=True)

    material = models.TextField(
        null=True, blank=True
    )  # should be taken from batch/product
    reproduction = models.BooleanField(default=False)
    refund = models.BooleanField(default=False)
    refund_reason = models.CharField(
        max_length=60,
        default='',
        blank=True,
    )

    assembly_team_intervention = models.BooleanField(default=False)
    fittings_only = models.BooleanField(default=False)

    additional_info = models.TextField(null=True, blank=True)

    logistic_additional_info = models.TextField(null=True, blank=True)
    production_additional_info = models.TextField(
        null=True,
        blank=True,
        help_text='Will be printed on frontview',
    )
    serialized_complaint_service = models.JSONField(default=dict)
    conversation_link = models.TextField(null=True, blank=True)

    tnt_complaint_type = models.CharField(
        max_length=250,
        null=True,
        blank=True,
        choices=TNTComplaintType.choices,
    )
    tnt_order_number = models.CharField(max_length=128, null=True, blank=True)
    tnt_tracking_number = models.CharField(max_length=128, null=True, blank=True)
    tnt_complaint_date = models.DateField(null=True, blank=True)
    tnt_notification_channel = models.CharField(
        max_length=128,
        choices=TNTNotificationChannel.choices,
        null=True,
        blank=True,
    )
    tnt_notification_by_customer = models.CharField(
        max_length=128,
        choices=TNTNotificationByCustomer.choices,
        null=True,
        blank=True,
    )
    tnt_additional_info = models.TextField(null=True, blank=True)
    tnt_status = models.CharField(
        max_length=128,
        choices=TNTStatus.choices,
        null=True,
        blank=True,
    )
    tnt_final_result = models.FloatField(null=True, blank=True)
    express_replacement = models.BooleanField(default=False)

    reproduction_element_categories = ArrayField(
        models.CharField(max_length=20),
        default=list,
    )

    created_at = models.DateTimeField(auto_now_add=True)
    closed_at = models.DateTimeField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    priority = models.PositiveSmallIntegerField(
        choices=enums.ComplaintPriorityChoices.choices,
        default=enums.ComplaintPriorityChoices.NORMAL,
        help_text=(
            'Is given Complaint was created as priority or normal. '
            'Priority Complaint are processed as first in order to be sent to customers'
        ),
    )

    complaint_costs = models.ForeignKey(
        ComplaintCosts,
        related_name='complaints',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )
    exported_to_big_query = models.DateTimeField(null=True, blank=True)
    has_deprecated_elements = models.BooleanField(default=False)

    # Remove after 3 fields below contacting with BI team 05.20.2024
    total_cost = models.DecimalField(
        blank=True, null=True, max_digits=7, decimal_places=2
    )
    product_cost = models.DecimalField(
        default=0.00, max_digits=7, decimal_places=2, verbose_name='COGS Product (€)'
    )

    complaint_cost = models.DecimalField(
        default=0.00, max_digits=7, decimal_places=2, verbose_name='COGS (€)'
    )
    created_by_assembly_team = models.BooleanField(default=False)
    is_confirmed = models.BooleanField(default=True)

    objects = ComplaintManager()

    @property
    def is_verified(self):
        return self.status not in {
            ComplaintStatus.VERIFICATION,
            ComplaintStatus.REJECTED,
        }

    @cached_property
    def complaintservice(self):
        from complaints.internal_api.serializers import ComplaintServiceDeserializer

        if not self.serialized_complaint_service:
            return None

        serializer = ComplaintServiceDeserializer(
            data=self.serialized_complaint_service
        )
        serializer.is_valid(raise_exception=True)
        return serializer.validated_data

    def set_serialized_complaint_service(self, serialized_complaint_service):
        self.serialized_complaint_service = serialized_complaint_service
        self.save(
            update_fields=['serialized_complaint_service'],
        )

    def get_order_id(self):
        return self.product.order_id

    def get_elements_description(self):
        return ', '.join(self.get_elements_with_number().elements)

    get_elements_description.short_description = 'Element list'

    def get_elements_with_number(self):
        elements = []
        count_of_elements = 0
        elements_counter = Counter(
            [x.split(' - BOX:')[0] for x in self.elements['elements']]
        )
        for name, number in elements_counter.items():
            elements.append(f'{name} x {number} szt')
            count_of_elements += number
        features_counter = Counter(
            [x.split(' - BOX:')[0] for x in self.elements['features']]
        )
        for name, number in features_counter.items():
            elements.append(f'{name} x {number} szt')
            count_of_elements += number
        for name in self.elements['other']:
            element_name_from_box_description = name.split(' - BOX:')[0]
            elements.append(f'{element_name_from_box_description} x 1 szt')
            count_of_elements += 1
        for name, number in self.elements['fittings'].items():
            elements.append(f'{name} x {number} szt')
            count_of_elements += number
        return ComplaintElementsWithAmount(elements, count_of_elements)

    @property
    def elements_for_reproduction(self):
        return list(
            chain(
                [x.split(' - BOX:')[0] for x in self.elements['elements']],
                [x.split(' - BOX:')[0] for x in self.elements['features']],
            )
        )

    @property
    def is_fittings_only_or_no_reproduction(self):
        return self.fittings_only or not self.reproduction_order

    @staticmethod
    def get_surname_from_element_name(element_name):
        if element_name.startswith('T'):
            # drawers with old names, remove last letter to get the element surname
            return element_name[:-1]
        if '-' in element_name:
            # drawers with new names, blendes from doors - surname is before the `-`
            return element_name.split('-')[0]
        return element_name

    def _map_surnames_to_elements_and_components(self, elements):
        """
        Return two dictionaries:
        - component_markers: {component_surname: (element_name, component_type)}
        - element_markers: {element_surname: [(element_name, None), ...]}
        """
        component_markers = {}
        element_markers = defaultdict(list)
        for element in elements:
            element_markers[element['surname']].append(
                (element['name'], 'element'),
            )
            components = element.get('components', []) or []
            for component in components:
                surname = component.get('surname')
                if self.product.is_sotty:
                    surname = (
                        f'{element.get("surname", "")}-{component.get("type", "")}'
                    )
                if surname:
                    component_markers[surname] = (element['name'], component['type'])
        return component_markers, element_markers

    def element_for_reproduction_with_full_names(self, product=None):
        elements_to_return = defaultdict(list)
        product = product or self.product

        elements_to_reproduce = self.elements['elements'] + self.elements['features']
        all_elements = product.get_cached_serialization_elements()
        (
            component_markers,
            element_markers,
        ) = self._map_surnames_to_elements_and_components(all_elements)
        for element_to_reproduce_with_box_name in elements_to_reproduce:
            element_surname = element_to_reproduce_with_box_name.split(' - BOX')[0]
            component = component_markers.get(element_surname)
            if component:
                elements_to_return[component[0]].append(component[1])
                continue
            # more than one element can have same surname (slab + led strips)
            elements = element_markers.get(element_surname, [])
            for element in elements:
                elements_to_return[element[0]].append(element[1])
        fittings = []
        for fitting_name, count in self.elements.get('fittings', {}).items():
            codename, _sep, tycode = fitting_name.partition(':')
            fittings.append({'codename': codename, 'count': count, 'tycode': tycode})
        return elements_to_return, fittings

    @property
    def is_aborted_done(self):
        return (
            self.previous_status == ComplaintStatus.DONE.value
            and self.status == ComplaintStatus.ABORTED.value
        )

    @property
    def can_be_aborted(self):
        return self.reproduction_order.status != OrderStatus.SHIPPED

    def complaints_count(self):
        # TODO optimize
        c_in_progress = Complaint.objects.filter(
            product__order__email=self.product.order.email,
            status__in=[ComplaintStatus.IN_PROGRESS, ComplaintStatus.NEW],
        ).exclude(pk=self.pk)
        if c_in_progress.count():
            return format_html(
                '<font style="font-size: 12px;">{}</font>',
                '</br>'.join([str(c.product_id) for c in c_in_progress]),
            )
        return 0

    complaints_count.short_description = 'CD'

    def get_elements_details(self, as_string=True):
        elements = self.product.get_elements_package_description(
            self.elements_for_reproduction,
        )
        if as_string:
            return json.dumps(elements)
        return elements

    def process_complaint_to_production(self) -> LogisticOrderDTO:
        from complaints.services.create_related_objects import (
            ProcessComplaintToProduction,
        )

        return ProcessComplaintToProduction.process_complaint_to_production(self)

    def update_production_complaint(self) -> LogisticOrderDTO:
        """
        Action for updating product serialization before batching.
        Should be triggered by ComplaintUpdateView in CS panel
        """
        if not self.reproduction:
            raise ComplaintNotReproductionException(
                'Only reproduction complaints can be updated'
            )
        if not self.reproduction_product:
            raise ComplaintNotReproductionProductException(
                'Only reproduction product complaints can updated'
            )
        self.refresh_serialization_and_costs_for_reproduction_product()
        return self.reproduction_product.get_logistic_order()

    def refresh_serialization_and_costs_for_reproduction_product(self):
        from producers.tasks import generate_product_files

        # update serialization after setting this product as reproduced in complaint
        cached_serialization = (
            self.reproduction_product.serialization_updater.update_product_serialization()  # noqa: E501
        )
        if cached_serialization:
            self.reproduction_product.reproduction_time_in_days = (
                self.get_reproduction_time_calculated()
            )
            self.reproduction_product.save()
        transaction.on_commit(
            lambda: generate_product_files.delay(self.reproduction_product.pk)
        )

    def update_complaint_cost(self, save=True):
        try:
            self.complaint_costs.complaint_cost = (
                get_fittings_only_cost(self)
                if self.fittings_only
                else get_reproduction_cost(self.reproduction_product)
            )
            self.complaint_costs.product_cost = get_product_cost(self.product)
        except EuroRateNotFound:
            return
        if save:
            self.complaint_costs.save(update_fields=['complaint_cost', 'product_cost'])

    def get_reproduction_time_calculated(self):

        if self.fittings_only:
            return ElementFitting.days

        element_details = get_element_details(
            elements=self.elements,
            product=self.product,
        )

        return max(
            element.reproduction_days_element.days for element in element_details
        )

    @classmethod
    def copy_gallery(cls, order_item):
        gallery = order_item.order_item
        gallery.pk = None
        gallery.save()
        return gallery

    def change_status(self, status_new, status_previous=None):
        if status_new == ComplaintStatus.ABORTED.value:
            abort_complaint(complaint=self)
        self.previous_status = status_previous or self.status
        self.status = status_new
        if status_new in {ComplaintStatus.DONE.value, ComplaintStatus.ABORTED.value}:
            self.closed_at = timezone.now()
        self.save(update_fields=['status', 'previous_status', 'closed_at'])
        ComplaintRefreshEvent(self)
        self._update_marketing_exclude_attributes()

    def save(self, *args, **kwargs):
        if self.id is not None:  # update
            self.exported_to_big_query = None
            history = ComplaintHistory()
            history.owner = self.owner
            history.complaint = self
            history.saved_version = ComplaintSerializer(self).data
            history.save()
        else:  # new complaint
            self._update_marketing_exclude_attributes()

        super(Complaint, self).save(*args, **kwargs)

    def delete(self, force_policy=None, **kwargs):
        MarketingExcludeUpdateEvent(
            user=self.product.order.owner,
            complaint_active=False,
            last_finished_complaint_date=timezone.now().date().isoformat(),
        )
        super().delete(force_policy=force_policy, **kwargs)

    def _update_marketing_exclude_attributes(self) -> None:
        """Emit event to update complaint related attributes in marketing excludes"""

        is_active = True
        last_finished_complaint_date = None

        if self.status in (ComplaintStatus.ABORTED, ComplaintStatus.DONE):
            is_active = False
            last_finished_complaint_date = timezone.now().date()

        MarketingExcludeUpdateEvent(
            user=self.product.order.owner,
            complaint_active=is_active,
            last_finished_complaint_date=last_finished_complaint_date,
        )


class ComplaintHistory(models.Model):
    owner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
    )
    complaint = models.ForeignKey(
        'complaints.Complaint',
        on_delete=models.CASCADE,
    )
    saved_version = models.JSONField()

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class LogisticComplaint(Complaint):
    class Meta(object):
        proxy = True


class ProductionComplaint(Complaint):
    class Meta(object):
        proxy = True


class AllComplaintCosts(Complaint):
    objects = ComplaintCostsManager()

    class Meta:
        proxy = True
        verbose_name_plural = 'All complaint costs'


class TNTCasesManager(models.Manager):
    def get_queryset(self):
        queryset = super().get_queryset()
        return queryset.exclude(tnt_complaint_type__isnull=True)


class TNTCasesComplaint(Complaint):
    objects = TNTCasesManager()

    class Meta(object):
        proxy = True


class CustomerContact(models.Model):
    first_name = models.CharField(max_length=64)
    email = models.EmailField()
    message = models.TextField(null=True)
    order = models.CharField(max_length=128, blank=True, null=True)
    topic = models.CharField(
        max_length=32,
        choices=enums.ContactTopic.choices(),
        default=enums.ContactTopic.OTHER.value,
    )
    has_missing_elements = models.BooleanField(default=False)
    missing_elements_description = models.TextField(blank=True)
    has_damaged_shelf_elements = models.BooleanField(default=False)
    has_damaged_packaging = models.BooleanField(default=False)
    damages_description = models.TextField(blank=True)
    is_reported_to_delivery_company = models.BooleanField(default=False)
    owner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        on_delete=models.CASCADE,
    )
    created = models.DateTimeField(auto_now_add=True)

    @property
    def message_with_damages(self):
        message = self.message or ''
        if self.damages_description.strip():
            message = '{}\n\nDamages description:\n{}'.format(
                message, self.damages_description
            )
        return message.strip()


def _prepare_filename(filename, path):
    ext = filename.split('.')[-1]
    filename = '{}.{}'.format(uuid4(), ext)
    dirname = datetime.now().strftime(DamageImage.IMAGES_PATH)
    return os.path.join(dirname, filename)


def complaint_file_name(instance, filename):
    return _prepare_filename(filename, ComplaintImage.IMAGES_PATH)


class ComplaintImage(models.Model):
    IMAGES_PATH = 'complaints/complaint_image/%Y/%m'
    created = models.DateTimeField(auto_now_add=True)
    image = models.ImageField(
        upload_to=complaint_file_name,
        validators=[
            FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'webp']),
            validate_image_size,
        ],
        storage=private_media_storage,
        max_length=150,
    )
    image_type = models.CharField(
        max_length=32,
        choices=enums.ImageType.choices(),
    )
    customer_contact = models.ForeignKey(
        CustomerContact,
        related_name='images',
        null=True,
        on_delete=models.CASCADE,
    )


def damage_form_image_file_name(instance, filename):
    return _prepare_filename(filename, DamageImage.IMAGES_PATH)


class DamageData(models.Model):
    ready_since = models.DateTimeField(null=True)
    status = models.PositiveSmallIntegerField(
        choices=enums.DamageFormStatus.choices, default=enums.DamageFormStatus.DRAFT
    )
    product = models.ForeignKey(
        'producers.Product',
        on_delete=models.CASCADE,
    )
    was_reported_to_delivery_company = models.BooleanField(
        verbose_name='Did the client report to the courier company the damage?',
        choices=BOOL_CHOICES,
    )
    are_any_elements_of_the_shelf_damaged = models.BooleanField(
        verbose_name='Are any elements of the shelf itself damaged?',
        choices=BOOL_CHOICES,
    )
    are_any_elements_missing = models.BooleanField(
        verbose_name='Are there any elements missing?',
        choices=BOOL_CHOICES,
    )
    list_of_damaged_elements = models.TextField()
    additional_comments = models.TextField(blank=True)

    def __str__(self):
        date_format = '%Y-%m-%d %H:%M'

        ready_since = ''
        if self.ready_since:
            ready_since = f' Ready since: {self.ready_since.strftime(date_format)}'

        return (
            f'Damage form for Product: {self.product.id}.'
            f'Status: {DamageFormStatus(self.status).name}.{ready_since}'
        )


class DamageImage(models.Model):
    IMAGES_PATH = 'complaints/damage_form_image/%Y/%m'
    created = models.DateTimeField(auto_now_add=True)
    image = models.ImageField(
        upload_to=damage_form_image_file_name,
        storage=private_media_storage,
        validators=[
            FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png']),
            validate_image_size,
        ],
        max_length=150,
    )
    image_type = models.CharField(
        max_length=32,
        choices=enums.ImageType.choices(),
    )
    damage_form_data = models.ForeignKey(DamageData, on_delete=models.CASCADE)

    def thumbnail(self):
        return f'<img src="{file_url(self.image.url)}" />'


class ReplaceableShelfFittingsStock(models.Model):
    code = models.CharField(max_length=9)
    name_pl = models.TextField()
    name_en = models.TextField()
    codename = models.CharField(max_length=150, blank=True)
    picture = models.ImageField(
        upload_to='complaints/replaceable_shelf_fittings_stock/%Y/%m',
        storage=private_media_storage,
        blank=True,
        null=True,
        max_length=150,
    )
    side = models.PositiveSmallIntegerField(
        choices=ReplaceableShelfFittingsStockSide.choices,
        default=ReplaceableShelfFittingsStockSide.NO_SIDE,
    )
    quantity = models.IntegerField(default=0)
    updated_at = models.DateTimeField(auto_now=True)


# DRF SERIALIZER
class ComplaintSerializer(serializers.ModelSerializer):
    class Meta(object):
        model = Complaint
        fields = '__all__'


class BaseComplaintPhoto(models.Model):
    file_name = models.CharField(max_length=255, blank=True)
    photo = models.FileField(
        blank=True,
        max_length=400,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('complaints/photo/%Y/%m'),
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        abstract = True


class SottyDamagePhoto(BaseComplaintPhoto):
    complaint = models.ForeignKey(
        'Complaint',
        related_name='sotty_photos',
        on_delete=models.CASCADE,
    )


class ComplaintPhoto(BaseComplaintPhoto):
    orders = models.ManyToManyField(
        'orders.Order', related_name='complaint_photos', blank=True
    )
    complaint = models.ForeignKey(
        'Complaint',
        related_name='photos',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    dixa_url_path = models.CharField(max_length=1000, blank=True)
    dixa_conversation_id = models.CharField(max_length=255, blank=True)
    email = models.EmailField(blank=True)


class AssemblyTeamComplaintPhoto(BaseComplaintPhoto):
    complaint = models.ForeignKey(
        'Complaint',
        related_name='assembly_team_photos',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )

    class Meta:
        verbose_name = 'Assembly Team Complaint Photo'
