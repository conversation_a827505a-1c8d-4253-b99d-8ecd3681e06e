import datetime
import logging

from calendar import monthrange
from datetime import timedelta

from django.contrib import (
    admin,
    messages,
)
from django.contrib.contenttypes.admin import GenericTabularInline
from django.db.models import (
    Count,
    J<PERSON><PERSON>ield,
)
from django.db.models.aggregates import Sum
from django.http import HttpResponseRedirect
from django.urls import (
    path,
    reverse,
)
from django.utils import timezone
from django.utils.html import (
    escape,
    format_html,
)
from django.utils.safestring import mark_safe

from django_fsm_log.admin import StateLogInline
from django_json_widget.widgets import JSONEditorWidget
from django_object_actions import DjangoObjectActions
from rangefilter.filters import DateRangeFilter

from custom.admin import SingletonAdmin
from custom.admin_action import admin_action_with_form
from custom.admin_mixins import ViewOnlyAdminMixin
from logger.admin import LoggerMixin
from orders.admin.actions import (
    capture_klarna_transactions_and_update_invoice,
    create_invoice,
    create_logistic_order_from_order,
    create_production_item_from_order,
    create_proforma_invoice,
    export_orders_as_csv,
    pay_for_order,
    recalculate_order,
    save_order_vat_and_redirect,
    unify_email_and_send_order_notifications,
    update_order_notes,
)
from orders.admin.filters import (
    EmptyCartFilter,
    OrderKlarnaPaymentFilter,
    OrderShelfCategoryFilter,
    OrderShelfTypeFilter,
    ReturningCustomersFilter,
    SellChannelFilter,
    StatusPaidFilter,
    UserProfileFilter,
    WattyFilter,
)
from orders.admin.mixins import OrderAdminMixin
from orders.enums import OrderType
from orders.exceptions import (
    OrderRegionPriceDiffThanPriceForEuroOrCHF,
    WrongOrderToProductionException,
)
from orders.forms import EditOrderVatForm
from orders.internal_api.events import (
    OrderRefreshEvent,
    OrderStatusChangedEvent,
)
from orders.models import (
    CustomOrder,
    MonthTarget,
    OfflineReferralOrder,
    Order,
    OrderBarterData,
    OrderItem,
    OrderStatusCheckHistory,
    OrderStatusHistory,
    OrderToProduction,
    OrderToProductionInvalid,
    PaidOrders,
    VIPOrder,
    VoucheredOrder,
)
from orders.services.process_to_production import ProcessOrderToProduction
from orders.tasks import (
    generate_vouchered_orders_report,
    import_and_send_nps,
)
from orders.validators import OrderToProductionValidator
from producers.models import Product

logger = logging.getLogger('orders')


@admin.display(description='preview')
def items_list_preview(obj):
    sum_pictures = ''
    for index, item in enumerate(obj.items.all()):
        sum_pictures += "<img src='{}' width='300px' />".format(
            escape(item.sellable_item.preview.url) if item.sellable_item.preview else ''
        )
        if (index + 1) % 3 == 0:
            sum_pictures += '<br/>'
    return mark_safe("<div style='width:450px; float:left'>" + sum_pictures + '</div>')


class ConfigurationInline(GenericTabularInline):
    raw_id_fields = ('order',)
    max_num = 0
    model = OrderItem


class ProductInline(admin.StackedInline):
    extra = 0
    model = Product
    fk_name = 'order_item'
    raw_id_fields = (
        'order',
        'order_item',
        'copy_of',
        'additional_order_items',
    )


class OrderItemAdmin(admin.ModelAdmin):
    raw_id_fields = (
        'free_return',
        'order',
    )
    model = OrderItem
    inlines = [ConfigurationInline, ProductInline]


@admin.display(description='preview')
def item_inline_preview(obj):
    if obj.sellable_item.preview:
        return mark_safe(
            '<img src="{}" width="600px" />'.format(
                escape(obj.sellable_item.preview.url)
            )
        )
    return mark_safe('<img src="/uploaded/" width="600px" />')


@mark_safe
@admin.display(description='Summary')
def orderdetails_summary(obj):
    resp_start = ''
    if obj.order_type != OrderType.CUSTOMER:
        resp_start = "<span style='color:red'>{}</span><br/>".format(
            obj.get_order_type_display()
        )
    if obj.company_name or obj.vat:
        resp_start += (
            f'{obj.first_name} {obj.last_name} <br/> '
            f'{obj.country} <br/> {obj.company_name} {obj.vat}'
        )
    elif obj.invoice_company_name or obj.invoice_vat:
        resp_start += (
            f'{obj.first_name} {obj.last_name} <br/> '
            f'{obj.country} <br/> {obj.invoice_company_name} {obj.invoice_vat}'
        )
    else:
        resp_start += f'{obj.first_name} {obj.last_name} <br/> {obj.country}'

    resp_start += (
        f"<br/><a href='/admin/accounting/order_info/{obj.id}/'> Summary view</a>"
    )

    return format_html(resp_start)


class OrderItemInline(admin.StackedInline):
    readonly_fields = (
        item_inline_preview,
        'item_display',
        'item_description',
        'object_id',
        'content_type',
        'price',
        'price_net',
        'assembly_price',
        'delivery_price',
        'vat_amount',
        'region_price',
        'region_price_net',
        'region_assembly_price',
        'region_delivery_price',
        'region_vat_amount',
        'region_promo_value',
        'region_delivery_promo_value',
        'recycle_tax_value',
    )

    fields = (
        item_inline_preview,
        'region',
        (
            'quantity',
            'with_assembly',
        ),
        'product_name',
        'invoice_product_name',
        ('object_id', 'content_type', 'item_display'),
        (
            'price',
            'price_net',
            'assembly_price',
            'delivery_price',
            'vat_amount',
        ),
        (
            'region_price',
            'region_price_net',
            'region_assembly_price',
            'region_delivery_price',
            'region_vat_amount',
            'region_promo_value',
            'region_delivery_promo_value',
            'recycle_tax_value',
        ),
        ('free_return',),
        ('item_description',),
    )

    model = OrderItem
    extra = 0
    raw_id_fields = ('order', 'free_return')

    def item_description(self, obj):
        return obj.order_item.description or ''

    @admin.display(description='Item')
    def item_display(self, instance):
        item = instance.order_item
        if not item:
            return 'No related object'

        content_type = instance.content_type
        url = reverse(
            f'admin:{content_type.app_label}_{content_type.model}_change',
            args=[item.pk],
        )
        title = str(item)
        return format_html('<a href="{}">{}</a>', url, title)


class PaidOrderAdmin(OrderAdminMixin, ViewOnlyAdminMixin, LoggerMixin):
    log_actions = True
    inlines = [OrderItemInline]
    list_display = (
        'id',
        'email',
        orderdetails_summary,
        'total_price',
        'paid_at',
        'get_items_as_string',
        'get_sell_channel',
        'order_source',
        items_list_preview,
        'referal',
    )
    list_display_links = ('id',)
    list_filter = (
        ('paid_at', DateRangeFilter),
        ('paid_at', ReturningCustomersFilter),
        'assembly',
        SellChannelFilter,
        UserProfileFilter,
        OrderShelfTypeFilter,
        OrderShelfCategoryFilter,
        'updated_at',
        'country',
    )
    actions = (
        create_production_item_from_order,
        create_invoice,
        create_proforma_invoice,
        export_orders_as_csv,
    )
    raw_id_fields = (
        'owner',
        'parent_order',
        'used_promo',
        'source_order_item',
        'target_order_item',
    )
    search_fields = (
        'id',
        'owner__username',
        'email',
        'invoice_email',
        'order_pretty_id',
    )

    ordering = ('-id',)
    date_hierarchy = 'paid_at'

    def get_queryset(self, request):
        """
        Returns a QuerySet of all model instances that can be edited by the
        admin site. This is used by changelist_view.
        """
        qs = (
            self.model.paid_orders.get_queryset()
            .filter(paid_at__isnull=False)
            .prefetch_related('items', 'invoice_set')
        )
        # TODO: this should be handled by some parameter to the ChangeList.
        ordering = self.get_ordering(request)
        if ordering:
            qs = qs.order_by(*ordering)
        return qs


class VoucheredOrderAdmin(OrderAdminMixin, ViewOnlyAdminMixin, admin.ModelAdmin):
    change_list_template = 'admin/vouchered_orders_change_list.html'
    inlines = [OrderItemInline]
    list_display = (
        'id',
        '__str__',
        'voucher_creator',
        'email',
        orderdetails_summary,
        'total_price',
        'total_price_net',
        'promo_amount',
        'promo_amount_net',
        'promo_text',
        'created_at',
        'updated_at',
        'paid_at',
        'order_pretty_id',
        'order_source',
    )
    list_display_links = ('id', '__str__', 'order_pretty_id')
    list_filter = (
        ('created_at', DateRangeFilter),
        UserProfileFilter,
        'updated_at',
        'paid_at',
        'order_source',
        StatusPaidFilter,
        'order_type',
        'used_promo__origin',
    )
    actions = [
        create_production_item_from_order,
        create_invoice,
        create_proforma_invoice,
        'generate_vouchered_order_report',
    ]
    raw_id_fields = (
        'owner',
        'parent_order',
        'used_promo',
    )
    search_fields = ('id', 'used_promo__code')
    ordering = ('-id',)
    date_hierarchy = 'paid_at'
    list_select_related = ['used_promo__creator']

    def changelist_view(self, request, extra_context=None):
        vouchers_stats = [
            vc
            for vc in Order.vouchered_orders.filter(paid_at__isnull=False)
            .values(
                'used_promo__code',
                'used_promo',
                'used_promo__kind_of',
                'used_promo__origin',
                'used_promo__value',
            )
            .annotate(num_v=Count('used_promo'), sum_v=Sum('promo_amount'))
            .order_by('-num_v')[:10]
        ]
        voucher_origin_stats = [
            vc
            for vc in Order.vouchered_orders.filter(paid_at__isnull=False)
            .values('used_promo__origin')
            .annotate(num_v=Count('used_promo__origin'), sum_v=Sum('promo_amount'))
            .order_by('-num_v')
        ]
        now = timezone.now()
        vouchers_stats_3m = [
            vc
            for vc in Order.vouchered_orders.filter(
                paid_at__isnull=False, paid_at__gte=now - timedelta(weeks=12)
            )
            .values(
                'used_promo__code',
                'used_promo',
                'used_promo__kind_of',
                'used_promo__origin',
                'used_promo__value',
            )
            .annotate(num_v=Count('used_promo'), sum_v=Sum('promo_amount'))
            .order_by('-num_v')[:10]
        ]
        voucher_origin_stats_3m = [
            vc
            for vc in Order.vouchered_orders.filter(
                paid_at__isnull=False, paid_at__gte=now - timedelta(weeks=12)
            )
            .values('used_promo__origin')
            .annotate(num_v=Count('used_promo__origin'), sum_v=Sum('promo_amount'))
            .order_by('-num_v')
        ]
        extra_context = {
            'vouchers_stats': vouchers_stats,
            'voucher_origin_stats': voucher_origin_stats,
            'vouchers_stats_3m': vouchers_stats_3m,
            'voucher_origin_stats_3m': voucher_origin_stats_3m,
        }
        return super(VoucheredOrderAdmin, self).changelist_view(request, extra_context)

    @admin.display(description='Voucher Creator')
    def voucher_creator(self, obj):
        return obj.used_promo.creator

    def generate_vouchered_order_report(self, request, queryset):
        email = request.user.email
        generate_vouchered_orders_report.delay(__to=(email,))

        self.message_user(
            request,
            f'Email with report will be sent to email: {email}',
        )


class OfflineReferralOrderAdmin(OrderAdminMixin, ViewOnlyAdminMixin, admin.ModelAdmin):
    show_full_result_count = False

    change_list_template = 'admin/offline_referral_orders_change_list.html'
    inlines = [OrderItemInline]
    list_display = (
        'id',
        '__str__',
        'email',
        orderdetails_summary,
        'promo_text',
        'total_price',
        'promo_amount',
        'created_at',
        'updated_at',
        'paid_at',
        'order_pretty_id',
        'order_source',
    )
    list_display_links = ('id', '__str__', 'order_pretty_id')
    list_filter = (
        ('paid_at', DateRangeFilter),
        UserProfileFilter,
        'updated_at',
        'created_at',
        'order_source',
        StatusPaidFilter,
        'order_type',
    )
    raw_id_fields = (
        'owner',
        'parent_order',
    )
    search_fields = ('id',)
    ordering = ('-id',)
    date_hierarchy = 'paid_at'

    def changelist_view(self, request, extra_context=None):
        orders_referral = OfflineReferralOrder.objects.count()
        orders_referral_added = (
            Product.objects.filter(
                order_id__in=OfflineReferralOrder.objects.values_list('id', flat=True)
            )
            .order_by('order_id')
            .distinct('order_id')
            .count()
        )
        extra_context = {
            'orders_referral': orders_referral,
            'orders_referral_added': orders_referral_added,
        }
        return super().changelist_view(request, extra_context)


class CustomOrderAdmin(OrderAdminMixin, ViewOnlyAdminMixin, LoggerMixin):
    log_actions = True
    inlines = [OrderItemInline]
    list_display = (
        'id',
        'order_pretty_id',
        'invoice',
        'items_in_production',
        'status',
        'created_at',
        'settled_at',
        'count_with_quantity',
        items_list_preview,
        'notes',
    )
    list_display_links = (
        'id',
        'order_pretty_id',
    )
    list_filter = (
        ('created_at', DateRangeFilter),
        UserProfileFilter,
        'status',
    )

    raw_id_fields = (
        'owner',
        'parent_order',
        'used_promo',
    )
    readonly_fields = (
        'switch_status',
        'source_order_item',
        'target_order_item',
        'completed_target_order_items',
        'source_total_price',
        'source_total_price_net',
        'source_region_total_price',
        'source_region_total_price_net',
    )

    search_fields = ('id', 'owner__username', 'email')
    ordering = ('-id',)
    date_hierarchy = 'paid_at'

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        return queryset.with_items_count_mul_quantity()

    @admin.display(description='Count with quantity')
    def count_with_quantity(self, obj: 'CustomOrder') -> int:
        return obj.count_with_quantity


@mark_safe
def order_str_with_overview(obj):
    return format_html(
        "{} <br/><a href='/admin/accounting/order_info/{}/'> Summary view</a>",
        obj,
        obj.id,
    )


class OrderBarterDataInline(admin.StackedInline):
    model = OrderBarterData
    extra = 0

    def has_add_permission(self, request, obj=None):
        return False

    def has_change_permission(self, request, obj=None):
        return False


class StateLogOrderSwitchInline(StateLogInline):
    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        order_add_extra_discount_transitions = [
            'commit_item_on_hold',
            'reset_switch_status_to_blank',
            'rollback_item_on_hold',
            'commit_item_replacement',
            'rollback_item_replacement',
            'commit_recalculations',
            'commit_additional_payment',
        ]
        return queryset.filter(transition__in=order_add_extra_discount_transitions)


class StateLogOrderAddExtraDiscountInline(StateLogInline):
    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        order_add_extra_discount_transitions = [
            'commit_discount_recalculation',
            'rollback_discount_recalculation',
            'commit_completed',
        ]
        return queryset.filter(transition__in=order_add_extra_discount_transitions)


class OrderAdmin(DjangoObjectActions, OrderAdminMixin, LoggerMixin):
    change_list_template = 'admin/order/change_list.html'
    show_full_result_count = False
    log_actions = True
    inlines = [
        OrderItemInline,
        StateLogOrderSwitchInline,
        StateLogOrderAddExtraDiscountInline,
        OrderBarterDataInline,
    ]

    list_display = (
        'id',
        order_str_with_overview,
        'status',
        'admin_country',
        'order_pretty_id',
        'total_price',
        'assembly',
        'updated_at',
        'order_type',
        'order_source',
        'cart_link',
    )
    list_display_links = ('id', order_str_with_overview)
    list_filter = (
        'status',
        'assembly',
        'updated_at',
        EmptyCartFilter,
        SellChannelFilter,
        WattyFilter,
        'order_type',
        StatusPaidFilter,
        'order_source',
        OrderKlarnaPaymentFilter,
    )

    exclude = ('estimated_delivery_time_log',)

    actions = (
        create_production_item_from_order,
        create_invoice,
        create_proforma_invoice,
        create_logistic_order_from_order,
        export_orders_as_csv,
        recalculate_order,
        unify_email_and_send_order_notifications,
        capture_klarna_transactions_and_update_invoice,
        pay_for_order,
        update_order_notes,
    )

    change_actions = ('update_order_vat',)

    raw_id_fields = (
        'owner',
        'parent_order',
        'used_promo',
    )

    ordering = ('-id',)
    # Overriden get_search_results!!!
    search_fields = ('id',)
    date_hierarchy = 'updated_at'
    date_hierarchy_drilldown = False

    readonly_fields = (
        'order_pretty_id',
        'cached_items_type',
        'vat',
        'hard_parking',
        'above_3rd_floor',
        'no_elevator',
        'floor_number',
        'region_vat',
        'vat_type',
        'total_price',
        'total_price_net',
        'region_total_price',
        'region_total_price_net',
        'promo_amount',
        'promo_amount_net',
        'region_promo_amount',
        'region_promo_amount_net',
        'serialized_logistic_info',
        'switch_status',
        'source_order_item',
        'target_order_item',
        'completed_target_order_items',
        'source_total_price',
        'source_total_price_net',
        'source_region_total_price',
        'source_region_total_price_net',
        'is_barter',
        'extra_discount_status',
        'completed_used_promos',
        'source_extra_discount_total_price',
        'source_extra_discount_total_price_net',
        'source_extra_discount_region_total_price',
        'source_extra_discount_region_total_price_net',
    )

    def cart_link(self, obj):
        if obj.cart and not obj.cart.deleted:
            return format_html(
                '<a href="{}">Cart {}</a>',
                f'/admin/carts/cart/{obj.cart.id}/change/',
                obj.cart.id,
            )
        return '-'

    def get_search_results(self, request, queryset, search_term):
        search_term = search_term.strip()
        if search_term and search_term.isdigit():
            return queryset.filter(id=int(search_term)), False
        return queryset, False

    def get_queryset(self, request):
        return (
            super(admin.ModelAdmin, self)
            .get_queryset(request)
            .select_related('owner')
            .prefetch_related('items', 'items__order_item')
        )

    @classmethod
    def should_logistic_order_be_updated(cls, form):
        tracked_fields = ('order_notes',) + Order.ADDRESS_FIELDS
        return any(field in form.changed_data for field in tracked_fields)

    def save_model(self, request, obj, form, change):
        if change and self.should_logistic_order_be_updated(form):
            OrderStatusChangedEvent(obj)
        if (
            change
            and 'estimated_delivery_time' in form.changed_data
            and Order.objects.get(pk=obj.pk).estimated_delivery_time
        ):
            obj.change_estimated_delivery_time(
                form.cleaned_data['estimated_delivery_time']
            )
            OrderRefreshEvent(obj)
        super().save_model(request, obj, form, change)

    @staticmethod
    def get_date_hierarchy_drilldown(year_lookup, month_lookup):
        """
        Override default behaviour to show only past dates.
        (from docs)
        """
        today = timezone.now().date()

        if year_lookup is None and month_lookup is None:
            # Past 3 years.
            return (
                datetime.date(y, 1, 1) for y in range(today.year - 2, today.year + 1)
            )

        elif year_lookup is not None and month_lookup is None:
            # Past months of selected year.
            this_month = today.replace(day=1)
            return (
                month
                for month in (
                    datetime.date(int(year_lookup), month, 1) for month in range(1, 13)
                )
                if month <= this_month
            )

        elif year_lookup is not None and month_lookup is not None:
            # Past days of selected month.
            days_in_month = monthrange(year_lookup, month_lookup)[1]
            return (
                day
                for day in (
                    datetime.date(year_lookup, month_lookup, i + 1)
                    for i in range(days_in_month)
                )
                if day <= today
            )

    @admin.action(description='Edit Vat')
    def update_order_vat(self, request, obj):
        return admin_action_with_form(
            modeladmin=self,
            request=request,
            instance=obj,
            queryset=[],
            form_class=EditOrderVatForm,
            form_initial={
                'vat': obj.vat,
            },
            success_function=save_order_vat_and_redirect,
            success_function_kwargs={'object_id': obj.id},
        )

    def get_urls(self):
        return [
            path(
                'import_nps/',
                self.import_nps,
            ),
        ] + super().get_urls()

    def import_nps(self, request):
        self.message_user(
            request,
            'Importing NPS, might take a while.',
        )
        import_and_send_nps.delay(request.user.email)
        return HttpResponseRedirect(reverse('admin:orders_order_changelist'))


class OrderStatusHistoryAdmin(admin.ModelAdmin):
    actions = None
    raw_id_fields = ('order',)
    list_display = ('id', 'order', 'status', 'previous_status', 'created_at')
    date_hierarchy = 'created_at'


class VIPOrderAdmin(OrderAdminMixin, ViewOnlyAdminMixin, admin.ModelAdmin):
    inlines = [OrderItemInline]
    list_display = (
        'id',
        'owner',
        'email',
        'country',
        'first_name',
        'promo_text',
        'total_price',
        'created_at',
        'updated_at',
        'status',
        'get_delivery_date',
        'estimated_delivery_time',
    )
    list_display_links = ('id',)
    list_filter = ('created_at', 'status', 'used_promo__code')
    raw_id_fields = (
        'owner',
        'parent_order',
        'used_promo',
    )
    search_fields = (
        'id',
        'owner__username',
        'email',
    )


class OrderStatusCheckHistoryAdmin(admin.ModelAdmin):
    actions = None
    list_display = (
        'id',
        'created_at',
        'order',
        'postal_code',
        'email',
        'order_status',
        'success',
    )
    date_hierarchy = 'created_at'


class OrderToProductionAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'original_order',
        'process_at',
        'is_klarna_payment',
    )
    raw_id_fields = ('original_order',)
    search_fields = ('id',)
    actions = ('process_paid_order_to_production',)
    formfield_overrides = {
        JSONField: {'widget': JSONEditorWidget},
    }

    def process_paid_order_to_production(self, request, queryset):
        if queryset.filter(process_at__isnull=False).exists():
            self.message_user(
                request,
                'Process not run. Please select only not processed orders',
                level=messages.ERROR,
            )
            return

        self.message_user(request, 'Run process_paid_order_to_production')

        for order_to_production in queryset:
            try:
                OrderToProductionValidator(order_to_production.original_order)()
            except OrderRegionPriceDiffThanPriceForEuroOrCHF as e:
                error = {
                    'exception_type': e.__class__.__name__,
                    'exception_message': str(e),
                }
                logger.warning(
                    f'Wrong Order: {order_to_production.original_order_id}: {error}'
                )
            except WrongOrderToProductionException as e:
                order_to_production.errors = [
                    {
                        'exception_type': e.__class__.__name__,
                        'exception_message': str(e),
                    }
                ]
                order_to_production.save(update_fields=['errors'])
                continue
            ProcessOrderToProduction(order_to_production).process()


class OrderToProductionInvalidAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'original_order',
        'errors',
        'is_klarna_payment',
    )
    raw_id_fields = ('original_order',)
    search_fields = ('id',)
    formfield_overrides = {
        JSONField: {'widget': JSONEditorWidget},
    }


class MonthTargetAdmin(SingletonAdmin, admin.ModelAdmin):
    list_display = ('wood_sample_boxes', 'material_sample_boxes', 'sample_orders')


admin.site.register(OrderToProduction, OrderToProductionAdmin)
admin.site.register(OrderToProductionInvalid, OrderToProductionInvalidAdmin)
admin.site.register(Order, OrderAdmin)
admin.site.register(PaidOrders, PaidOrderAdmin)
admin.site.register(VoucheredOrder, VoucheredOrderAdmin)
admin.site.register(OfflineReferralOrder, OfflineReferralOrderAdmin)
admin.site.register(CustomOrder, CustomOrderAdmin)
admin.site.register(OrderItem, OrderItemAdmin)
admin.site.register(VIPOrder, VIPOrderAdmin)
admin.site.register(OrderStatusHistory, OrderStatusHistoryAdmin)
admin.site.register(OrderStatusCheckHistory, OrderStatusCheckHistoryAdmin)
admin.site.register(MonthTarget, MonthTargetAdmin)
