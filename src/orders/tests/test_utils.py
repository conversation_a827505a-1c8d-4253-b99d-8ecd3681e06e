import pytest

from custom.internal_api.enums import AssemblyTypeChoices
from custom.tests.factories import LogisticOrderDTOFactory
from orders.models import Order
from orders.utils import (
    get_assembly_type,
    update_carrier,
)


@pytest.mark.parametrize(
    ('order_number', 'expected'),
    (
        ('RV/00237/10/2019/20931204/DE', 20931204),
        ('R/454504/0120931204/DE', 20931204),
        ('20911204', 20911204),
    ),
)
def test_deserialize_order_number(order_number, expected):
    deserialized_order_number = Order.deserialize_order_number(order_number)
    assert deserialized_order_number == expected


@pytest.mark.django_db
def test_send_order_notifications(
    mocker,
    order_factory,
):
    data = {
        'email_address': '<EMAIL>',
        'order_confirmation': True,
        'payment_confirmation': True,
        'invoice_email': True,
    }
    form = mocker.Mock()
    form.cleaned_data = data

    order_confirmation_mail = mocker.patch(
        'orders.models.Order.send_order_placed_email'
    )
    payment_confirmation = mocker.patch('orders.models.Order.send_payment_confirmation')
    invoice_email = mocker.patch('orders.models.Order.send_invoice_email')

    order = order_factory()
    order.send_order_notifications(
        email_address=form.cleaned_data['email_address'],
        order_confirmation=form.cleaned_data['order_confirmation'],
        payment_confirmation=form.cleaned_data['payment_confirmation'],
        invoice_email=form.cleaned_data['invoice_email'],
    )

    assert order_confirmation_mail.assert_called_once
    assert payment_confirmation.assert_called_once
    assert invoice_email.assert_called_once


@pytest.mark.django_db
class TestAssemblyType:
    @pytest.mark.parametrize(
        'assembly,assembly_type_choice',
        [
            (False, None),
            (True, AssemblyTypeChoices.ASSEMBLY_PAID),
        ],
    )
    def test_get_assembly_type_jetty(
        self, assembly, assembly_type_choice, order_factory, order_item_factory
    ):
        order = order_factory(assembly=assembly)
        order_item = order_item_factory(order=order, is_jetty=True)
        assert (
            get_assembly_type(order, order_item=order_item.order_item)
            == assembly_type_choice
        )

    @pytest.mark.parametrize(
        'assembly,assembly_type_choice',
        [
            (False, AssemblyTypeChoices.ASSEMBLY_INCLUDED),
            (True, AssemblyTypeChoices.ASSEMBLY_INCLUDED),
        ],
    )
    def test_get_assembly_type_watty(
        self, assembly, assembly_type_choice, order_factory, order_item_factory
    ):
        order = order_factory(assembly=assembly)
        order_item = order_item_factory(order=order, is_watty=True)
        assert (
            get_assembly_type(order, order_item=order_item.order_item)
            == assembly_type_choice
        )


@pytest.mark.django_db
class TestUpdateCarrier:
    def test_update_carrier_success(self, mocker, order_factory):
        order = order_factory()
        logistic_order_id = 123
        initial_carrier = 'DPD'
        new_carrier = 'TNT'

        LogisticOrderDTOFactory(id=logistic_order_id, carrier=initial_carrier)

        order.serialized_logistic_info = [
            {'id': logistic_order_id, 'carrier': initial_carrier}
        ]

        carrier_data = [
            {
                'order_id': order,
                'logistic_order_id': logistic_order_id,
                'carrier': new_carrier,
            }
        ]
        updated_orders = update_carrier(carrier_data)

        assert len(updated_orders) == 1
        assert updated_orders[0] == order
        assert order.serialized_logistic_info[0]['carrier'] == new_carrier

    def test_update_carrier_empty_list(self):
        carrier_data = []
        updated_orders = update_carrier(carrier_data)
        assert len(updated_orders) == 0

    def test_update_carrier_invalid_logistic_order_id(self, mocker, order_factory):
        order = order_factory()
        logistic_order_id = 123
        new_carrier = 'TNT'

        carrier_data = [
            {
                'order_id': order,
                'logistic_order_id': logistic_order_id,
                'carrier': new_carrier,
            }
        ]
        updated_orders = update_carrier(carrier_data)

        assert len(updated_orders) == 0
