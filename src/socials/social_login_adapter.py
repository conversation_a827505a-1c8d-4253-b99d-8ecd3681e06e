from allauth.account.adapter import Default<PERSON>ccountAdapter
from allauth.socialaccount.adapter import DefaultSocialAccountAdapter

from carts.models import User
from regions.services.region_selector import RegionSelector
from user_profile.choices import UserType
from user_profile.services.user_profile_transfer import UserProfileTransferService


class AccountAdapter(DefaultAccountAdapter):
    def pre_login(self, request, user, *args, **kwargs):
        super(AccountAdapter, self).pre_login(request, user, *args, **kwargs)
        if getattr(request, 'source_user', None):
            transfer_service = UserProfileTransferService(
                source=request.source_user, target=user
            )
            transfer_service.transfer_cart()

    def post_login(
        self,
        request,
        user,
        *,
        email_verification,
        signal_kwargs,
        email,
        signup,
        redirect_url,
    ):
        if redirect_url and 'checkout' in redirect_url:
            region_prefix, _ = redirect_url.split('checkout')
            redirect_url = f'{region_prefix}{user.profile.get_checkout_url()}'
        return super().post_login(
            request,
            user,
            email_verification=email_verification,
            signal_kwargs=signal_kwargs,
            email=email,
            signup=signup,
            redirect_url=redirect_url,
        )


class SocialAccountAdapter(DefaultSocialAccountAdapter):
    def save_user(self, request, sociallogin, form=None):
        user = super().save_user(request, sociallogin, form=None)
        # profile is added through signal in user_profile/models/models.py
        if not user.profile.region:
            region_selector = RegionSelector(request)
            region = region_selector.get_region()
            user.profile.region = region
            user.profile.save(update_fields=['region'])
        return user

    def populate_user(self, request, sociallogin, data):
        user = super().populate_user(request, sociallogin, data)
        # cause of by default email is not equal to username,
        # when user created by social login
        # we need to set it here by email field
        if user.email and user.username != user.email:
            if not User.objects.filter(username=user.email).exists():
                user.username = user.email
        return user

    def pre_social_login(self, request, sociallogin):
        self._dump_guest_user(request)
        if not sociallogin.is_existing:
            self._connect_to_existing_user(request, sociallogin)

    def _dump_guest_user(self, request):
        if (
            request.user.is_authenticated
            and not request.user.is_anonymous
            and request.user.profile.user_type == UserType.GUEST_CUSTOMER
        ):
            request.source_user = request.user

    def _connect_to_existing_user(self, request, sociallogin):
        email = None
        for email_address in sociallogin.email_addresses:
            if email_address.verified:
                email = email_address.email
                break
        email = email or sociallogin.account.extra_data.get('email')
        if not email:
            return
        try:
            user = User.objects.get(username=email)
            sociallogin.connect(request, user)
        except (User.DoesNotExist, User.MultipleObjectsReturned):
            return
