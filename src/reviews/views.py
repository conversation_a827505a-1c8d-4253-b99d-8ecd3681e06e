import logging

from django.conf import settings
from django.http.response import HttpResponse
from django.shortcuts import get_object_or_404
from django.views.generic.base import TemplateView

from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import (
    mixins,
    status,
)
from rest_framework.filters import Ordering<PERSON><PERSON>er
from rest_framework.pagination import PageNumberPagination
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import GenericViewSet
from sorl.thumbnail.shortcuts import get_thumbnail

from ecommerce_api.settings import api_settings
from reviews.filters import (
    ReviewExtendedFilter,
    ReviewFilter,
)
from reviews.models import (
    Review,
    ReviewScore,
    ReviewTag,
)
from reviews.serializers import (
    CreateReviewSerializer,
    SingleReviewSerializer,
    ToolReviewListSerializer,
    ToolReviewSerializer,
    ToolReviewTagSerilizer,
    ToolReviewUpdateSerializer,
)
from reviews.tasks import (
    match_review_with_order_task,
    send_bad_review_to_slack,
)

logger = logging.getLogger('cstm')


class ReviewsPagination(PageNumberPagination):
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 1000


class ReviewPhotoUrlView(APIView):
    swagger_tags = ['Reviews']
    permission_classes = ()

    def get(self, request, pk):
        review = get_object_or_404(Review, pk=pk, photos__isnull=False)
        last_image_review = review.photos.last()
        if last_image_review is None:
            return HttpResponse(
                '', content_type='text/plain', status=status.HTTP_200_OK
            )
        else:
            thumbnail = get_thumbnail(review.photos.last().image, '1200x800')
            return HttpResponse(
                str(thumbnail.url), content_type='text/plain', status=status.HTTP_200_OK
            )


class ReviewToolView(TemplateView):
    template_name = 'admin_custom/review_tool.html'


class ReviewToolViewSet(
    mixins.RetrieveModelMixin,
    mixins.DestroyModelMixin,
    mixins.UpdateModelMixin,
    mixins.ListModelMixin,
    GenericViewSet,
):
    queryset = Review.latest_objects.order_by('-created_at')
    pagination_class = ReviewsPagination
    filter_backends = [DjangoFilterBackend]
    filterset_class = ReviewExtendedFilter

    def get_serializer_class(self):
        if self.action == 'list':
            return ToolReviewListSerializer
        elif self.action == 'update':
            return ToolReviewUpdateSerializer

        return ToolReviewSerializer

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        pk = kwargs.get('pk')
        next_review_id = (
            Review.latest_objects.filter(id__gt=pk).values_list('id', flat=True).first()
        )
        previous_review_id = (
            Review.latest_objects.filter(id__lt=pk).values_list('id', flat=True).first()
        )
        return Response(
            {
                **serializer.data,
                'next_review_id': next_review_id,
                'previous_review_id': previous_review_id,
            }
        )


class ReviewViewSet(
    mixins.ListModelMixin,
    mixins.CreateModelMixin,
    GenericViewSet,
):
    swagger_tags = ['Reviews']
    queryset = Review.latest_objects.filter(enabled=True)
    pagination_class = ReviewsPagination
    permission_classes = (AllowAny,)
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    ordering_fields = ['score', 'created_at']
    filterset_class = ReviewFilter
    renderer_classes = api_settings.ECOMMERCE_RENDERER_CLASSES

    def create(self, request, *args, **kwargs):
        context = self.get_serializer_context()
        serializer = self.get_serializer(data=request.data, context=context)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        match_review_with_order_task.delay(serializer.data['pk'])
        if settings.IS_PRODUCTION and serializer.data['score'] < 5:
            send_bad_review_to_slack.delay(serializer.data['pk'])
        return Response(
            serializer.data,
            status=status.HTTP_201_CREATED,
            headers=headers,
        )

    def get_serializer_class(self):
        if self.action == 'create':
            return CreateReviewSerializer
        return SingleReviewSerializer

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['language'] = self.request.LANGUAGE_CODE
        context['user_profile'] = (
            self.request.user.profile if self.request.user.is_authenticated else None
        )
        return context


class ReviewTagsApiView(APIView):
    authentication_classes = []
    permission_classes = ()

    def get(self, request):
        review = ToolReviewTagSerilizer(ReviewTag.objects.all(), many=True).data
        return Response(review)


class GlobalReviewScoreApiView(APIView):
    authentication_classes = []
    permission_classes = [AllowAny]
    swagger_tags = ['Reviews']

    def get(self, request):
        avg_score, reviews_count = ReviewScore.get_general_review_score()
        return Response(
            {'avg_score': avg_score, 'reviews_count': reviews_count},
            status=status.HTTP_200_OK,
        )
