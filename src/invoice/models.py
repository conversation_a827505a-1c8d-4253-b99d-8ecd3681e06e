import itertools
import json
import logging
import uuid

from copy import deepcopy
from datetime import (
    UTC,
    date,
    datetime,
    timedelta,
)
from decimal import (
    ROUND_HALF_UP,
    Decimal,
)
from operator import attrgetter
from subprocess import CalledProcessError
from typing import (
    TYPE_CHECKING,
    Optional,
)

from django.conf import settings
from django.contrib.contenttypes.models import ContentType
from django.core.files.base import ContentFile
from django.db import (
    connection,
    models,
    transaction,
)
from django.db.models import (
    Q,
    QuerySet,
    Sum,
    Value,
)
from django.db.models.functions import Coalesce
from django.db.transaction import atomic
from django.utils import timezone
from django.utils.encoding import force_str
from django.utils.safestring import mark_safe

from past.utils import old_div

from cstm_be.media_storage import private_media_storage
from custom.constants import (
    VAT_EU,
    VAT_NORMAL,
)
from custom.models import (
    Countries,
    ExchangeRate,
)
from custom.utils.decorators import cache_model_method
from custom.utils.files import render_pdf_from_template
from customer_service.enums import (
    CSCorrectionRequestStatus,
    CSCorrectionRequestType,
)
from events.domain_events.transact_events import InvoiceReadyEvent
from events.utils import map_invoice_status_to_type
from gallery.models import Sotty
from invoice.constants import (
    CURRENCY_SYMBOL_TO_IBAN_NUMBER,
    DEFAULT_IBAN,
    HTS_CODE,
    HTS_CODE_BY_FURNITURE_TYPE,
)
from invoice.encoder import (
    TypedJSONEncoder,
    decode_typed_strings,
)
from invoice.enums import InvoiceItemTag
from invoice.managers import (
    InvoiceAllManager,
    InvoiceDomesticAllManager,
    InvoiceDomesticManager,
    InvoiceManager,
    InvoicePreviewAllManager,
    InvoicePreviewManager,
    ProformaCstmInvoiceManager,
    ProformaCustomerServiceInvoiceManager,
    ProformaLogisticFreeMaterialSampleOutsideEUInvoiceManager,
    ProformaLogisticInvoiceManager,
    ProformaRetoolServiceInvoiceManager,
)
from logger.models import Log
from mailing.templates import (
    OrderCorrectionInvoiceFreeReturnKlarnaMail,
    OrderCorrectionInvoiceFreeReturnNormalMail,
    OrderCorrectionInvoiceMail,
    OrderInvoiceKlarnaMail,
    OrderInvoiceMail,
    OrderInvoiceProformaMail,
)
from orders.enums import OrderStatus
from orders.services.vat_details import VatDetailsGetter
from regions.constants import OTHER_REGION_NAME
from regions.models import (
    Country,
    Currency,
)
from services.models import AdditionalService

from .choices import (
    InvoiceChangeScope,
    InvoiceItemType,
    InvoiceSource,
    InvoiceStatus,
    NumerationType,
    VatType,
)
from .from_order_item_dto import (
    AdditionalServiceItemFromOrderItem,
    AssemblyInvoiceItemFromOrderItem,
    DeliveryItemFromOrderItem,
    FreeSampleInvoiceItemFromOrderItem,
    InvoiceItemFromOrderItem,
)
from .render_strategies import (
    ServiceAsSeparateEntryStrategy,
    ServiceCombinedWithItemStrategy,
    earliest_invoice_with_services_version_not_supported,
)
from .templatetags.invoice_tags import as_percentage
from .utils import calculate_vat_amount

if TYPE_CHECKING:
    from orders.models import OrderItem


logger = logging.getLogger('invoice')

tzinfo = UTC if settings.USE_TZ else None
DOMESTIC_RELEASE_DATETIME = datetime(2024, 7, 1, 11, 15, 0, tzinfo=tzinfo)


class Invoice(models.Model):
    INVOICE_ITEM_FROM_ORDER_CLASS = InvoiceItemFromOrderItem

    UNITED_KINGDOM_TAX_INCLUDED_TEXT = 'Included 20 % VAT GB'
    UNITED_KINGDOM_TAX_INCLUDED_VAT_RATE = Decimal('0.2')
    UNITED_KINGDOM_TAX_THRESHOLD = Decimal('135.0')

    order = models.ForeignKey(
        'orders.Order',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )
    source = models.PositiveSmallIntegerField(
        default=InvoiceSource.CSTM,
        choices=InvoiceSource.choices,
        db_index=True,
    )
    # Order the invoice services
    pretty_id = models.CharField(max_length=36, blank=True)
    receipt_id = models.CharField(max_length=64, blank=True)
    # Pretty identification in form of
    # <F-invoice|R-receipt>/<invoice_no><month><year>/
    # <project_id><order_id>/<country_code>
    created_at = models.DateTimeField(auto_now_add=True)
    pdf = models.FileField(
        upload_to='invoice/invoice/%Y/%m',
        max_length=150,
        storage=private_media_storage,
        blank=True,
        null=True,
    )
    status = models.PositiveSmallIntegerField(
        default=InvoiceStatus.ENABLED, choices=InvoiceStatus.choices, db_index=True
    )
    currency_symbol = models.CharField(default='€', max_length=10)
    force_outside_eu = models.BooleanField(default=False)

    # dates
    issued_at = models.DateTimeField(blank=True, null=True)
    corrected_issued_at = models.DateTimeField(blank=True, null=True)
    sell_at = models.DateTimeField(blank=True, null=True)
    corrected_sell_at = models.DateTimeField(blank=True, null=True)
    due_date_at = models.DateTimeField(blank=True, null=True)
    delivery_date_at = models.DateTimeField(blank=True, null=True)
    receipt_date_at = models.DateTimeField(blank=True, null=True)
    receipt_date_change = models.DateTimeField(blank=True, null=True)
    sent_invoice_at = models.DateTimeField(null=True, blank=True)
    payable_booking_date = models.DateTimeField(blank=True, null=True)

    # saved vat/rates
    exchange_date = models.DateField(blank=True, null=True)
    exchange_rate = models.DecimalField(
        max_digits=16, decimal_places=4, blank=True, null=True
    )
    vat_amount_in_pln = models.DecimalField(
        max_digits=16, decimal_places=2, blank=True, null=True
    )

    # additional fields (monoqui and custom invoices)
    additional_top = models.TextField(blank=True, null=True)
    show_both_address = models.BooleanField(default=False)
    additional_address_1 = models.TextField(blank=True, null=True)
    additional_address_2 = models.TextField(blank=True, null=True)
    use_polish_account_in_pln = models.BooleanField(default=False)

    # for diffrent currency total
    additional_total_text = models.CharField(max_length=120, blank=True, null=True)
    additional_total_value = models.CharField(max_length=120, blank=True, null=True)

    corrected_invoice = models.ForeignKey(
        'Invoice',
        blank=True,
        null=True,
        related_name='corrections',
        on_delete=models.SET_NULL,
    )
    corrected_notes = models.TextField(blank=True, null=True)
    previous_correction = models.ForeignKey(
        'Invoice',
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )

    # override things for broken invoices

    force_net = models.DecimalField(
        max_digits=12, decimal_places=2, null=True, blank=True
    )
    force_vat = models.DecimalField(
        max_digits=12, decimal_places=2, null=True, blank=True
    )
    force_gross = models.DecimalField(
        max_digits=12, decimal_places=2, null=True, blank=True
    )

    force_net_in_pln = models.DecimalField(
        max_digits=12, decimal_places=2, null=True, blank=True
    )
    force_vat_in_pln = models.DecimalField(
        max_digits=12, decimal_places=2, null=True, blank=True
    )
    force_gross_in_pln = models.DecimalField(
        max_digits=12, decimal_places=2, null=True, blank=True
    )

    force_exchange_rate = models.DecimalField(
        max_digits=8, decimal_places=4, null=True, blank=True
    )
    force_exchange_date = models.DateField(null=True, blank=True)

    exported_to_symfonia = models.DateTimeField(null=True, blank=True, default=None)
    exported_to_symfonia_f = models.DateTimeField(null=True, blank=True, default=None)

    is_domestic = models.BooleanField(default=False)

    cached_to_dict = models.JSONField(default=dict)
    cached_delivery_address_country = models.CharField(blank=True, max_length=255)

    objects = InvoiceManager()
    all_objects = InvoiceAllManager()

    def __str__(self):
        return 'Invoice[id={} pretty_id={} order="{}" created_at="{}"]'.format(
            self.id, self.pretty_id, self.order_id, self.created_at
        )

    def get_domestic_invoice(self):
        if not self.order.is_united_kingdom():
            return None
        domestic_pretty_id = f'{self.pretty_id}/GB'
        return InvoiceDomestic.objects.filter(
            pretty_id=domestic_pretty_id,
            issued_at=self.issued_at,
        ).first()

    def get_corrected_invoice(self):
        if self.is_domestic:
            source_pretty_id = self.pretty_id.replace('/GB', '')
            invoice = Invoice.objects.get(pretty_id=source_pretty_id)
        else:
            invoice = self
        previous_invoice = invoice.previous_correction or invoice.corrected_invoice
        return previous_invoice

    def is_outside_eu(self):
        region = self.order.get_region(dont_use_cache=True)
        return not region.is_eu

    def is_united_kingdom_domestic(self):
        return self.order.is_united_kingdom() and self.is_domestic

    @property
    def region_total_value(self):
        return self.to_dict()['total_value']

    def set_additional_total_when_united_kingdom_lte_tax_threshold(
        self, region_total_diff: Decimal
    ) -> None:
        self.additional_total_text = self.UNITED_KINGDOM_TAX_INCLUDED_TEXT
        included_united_kingdom_vat = calculate_vat_amount(
            region_total_diff,
            self.UNITED_KINGDOM_TAX_INCLUDED_VAT_RATE,
        )

        self.additional_total_value = (
            f'{included_united_kingdom_vat}{self.currency_symbol}'
        )
        self.save(update_fields=['additional_total_text', 'additional_total_value'])

    def match_log(self):
        log = Log.objects.filter(model=self._meta.label).filter(
            Q(model_id=self.pk) | Q(model_id=None, data__contains=[self.pk])
        )
        return log

    def get_currency_code(self):
        try:
            currency_code = Currency.objects.get(symbol=self.currency_symbol).code
        except Currency.DoesNotExist:
            currency_code = self.order.get_region(dont_use_cache=True).currency.code
        return currency_code

    def get_invoice_items(self):
        return self.invoice_items.all()

    def filter_service_items(self):
        return self.invoice_items.filter(
            item_type__in=[
                InvoiceItemType.DELIVERY,
                InvoiceItemType.ASSEMBLY,
                InvoiceItemType.FAST_TRACK,
            ]
        )

    def filter_invoice_items(self):
        return self.invoice_items.filter(item_type=InvoiceItemType.ITEM)

    def get_order_items(self) -> QuerySet['OrderItem']:
        return self.order.material_items.all().order_by('price')

    def get_sotty_items(self) -> QuerySet['OrderItem']:
        sotty_ct = ContentType.objects.get_for_model(Sotty)
        order_items = self.order.items.filter(content_type=sotty_ct)
        return order_items.order_by('price')

    def get_order_service_items(self) -> QuerySet['OrderItem']:
        additional_service_ct = ContentType.objects.get_for_model(AdditionalService)
        service_items = self.order.items.filter(content_type=additional_service_ct)
        return service_items.order_by('price')

    @property
    def is_correction(self):
        return self.status in InvoiceStatus.correcting_statuses()

    def invoice_sent_at(self):
        if self.should_hide_sent_at_when_non_domestic_united_kingdom():
            return 'UK'
        if self.order is not None and self.sent_invoice_at is not None:
            return self.sent_invoice_at
        return 'not yet'

    def should_hide_sent_at_when_non_domestic_united_kingdom(self):
        return (
            self.order
            and self.sent_invoice_at is None
            and not self.is_domestic
            and self.order.earliest_invoice_domestic_version_supported()
        )

    def recreate_items(self) -> None:
        self.invoice_items.all().delete()
        self._create_items()

    def should_create_assembly_service(self, order_item: 'OrderItem') -> bool:
        return self.order.assembly and not order_item.is_samplebox()

    def should_create_delivery_service(self, order_item: 'OrderItem') -> bool:
        return order_item.is_sotty()

    def _create_items(self, ignore_discount=False) -> None:
        ignore_promo_on_invoice = ignore_discount or (
            self.order.used_promo and self.order.used_promo.ignore_on_invoice
        )

        vat_status = self.get_vat_type()
        vat_rate = self.get_vat_rate()

        for order_item in self.get_order_items():
            self.create_item(ignore_promo_on_invoice, order_item, vat_rate, vat_status)
            self.create_assembly_item(order_item, vat_rate, vat_status)
            self.create_delivery_item(
                ignore_promo_on_invoice, order_item, vat_rate, vat_status
            )
        for service_item in self.get_order_service_items():
            self.create_additional_service_item(
                ignore_promo_on_invoice, service_item, vat_rate, vat_status
            )

        invoice_items_total = self.sum_invoice_items_gross_price()
        order_total = self.order.get_total_value()

        if invoice_items_total != order_total:
            diff_to_add = Decimal(order_total) - Decimal(invoice_items_total)
            order_promo_amount = self.order.promo_amount
            if order_promo_amount != diff_to_add:
                logger.error(
                    'Too much difference between invoice items gross price '
                    'and order(id=%s). Difference=%s, Order promo_amount=%s',
                    self.order.id,
                    diff_to_add,
                    order_promo_amount,
                )

    def create_item(
        self,
        ignore_promo_on_invoice: bool,
        order_item: 'OrderItem',
        vat_rate: Decimal,
        vat_status: int,
    ) -> 'InvoiceItem':
        invoice_item_dto = self.INVOICE_ITEM_FROM_ORDER_CLASS(
            order_item,
            self.order,
            self,
            ignore_promo_on_invoice,
        )
        dimension, material, name = invoice_item_dto.get_descriptions()
        gross_weight, net_weight = invoice_item_dto.get_weights()

        return InvoiceItem.objects.create(
            invoice=self,
            order_item=order_item,
            item_type=InvoiceItemType.ITEM,
            item_name=name,
            item_material=material,
            item_dimensions=dimension,
            quantity=order_item.quantity,
            vat_rate=vat_rate,
            vat_status=vat_status,
            net_price=invoice_item_dto.net_price_per_item,
            discount_value=invoice_item_dto.discount_value_per_item,
            net_value=invoice_item_dto.net_value_total,
            vat_amount=invoice_item_dto.vat_amount_total,
            gross_price=invoice_item_dto.gross_price_total,
            recycle_tax_value=invoice_item_dto.recycle_tax_value,
            net_weight=net_weight,
            gross_weight=gross_weight,
        )

    def create_delivery_item(
        self,
        ignore_promo_on_invoice: bool,
        order_item: 'OrderItem',
        vat_rate: Decimal,
        vat_status: int,
    ) -> Optional['InvoiceItem']:
        if not self.should_create_delivery_service(order_item):
            return None
        invoice_delivery_dto = DeliveryItemFromOrderItem(
            order_item,
            self.order,
            self,
            ignore_promo_on_invoice,
        )

        _, _, name = invoice_delivery_dto.get_descriptions()

        return InvoiceItem.objects.create(
            invoice=self,
            order_item=order_item,
            item_type=InvoiceItemType.DELIVERY,
            item_name=name,
            quantity=order_item.quantity,
            vat_rate=vat_rate,
            vat_status=vat_status,
            net_price=invoice_delivery_dto.net_price_per_item,
            discount_value=invoice_delivery_dto.discount_value_per_item,
            net_value=invoice_delivery_dto.net_value_total,
            vat_amount=invoice_delivery_dto.vat_amount_total,
            gross_price=invoice_delivery_dto.gross_price_total,
            recycle_tax_value=Decimal('0.00'),
        )

    def create_assembly_item(
        self,
        order_item: 'OrderItem',
        vat_rate: Decimal,
        vat_status: int,
    ) -> Optional['InvoiceItem']:
        if not self.should_create_assembly_service(order_item):
            return None

        invoice_assembly_dto = AssemblyInvoiceItemFromOrderItem(
            order_item,
            self.order,
            self,
        )

        _, _, name = invoice_assembly_dto.get_descriptions()

        return InvoiceItem.objects.create(
            invoice=self,
            order_item=order_item,
            item_type=InvoiceItemType.ASSEMBLY,
            item_name=name,
            quantity=order_item.quantity,
            vat_rate=vat_rate,
            vat_status=vat_status,
            net_price=invoice_assembly_dto.net_price_per_item,
            discount_value=Decimal('0.00'),
            net_value=invoice_assembly_dto.net_value_total,
            vat_amount=invoice_assembly_dto.vat_amount_total,
            gross_price=invoice_assembly_dto.gross_price_total,
            recycle_tax_value=Decimal('0.00'),
        )

    def create_additional_service_item(
        self,
        ignore_promo_on_invoice: bool,
        order_item: 'OrderItem',
        vat_rate: Decimal,
        vat_status: int,
    ) -> 'InvoiceItem':

        service_item_dto = AdditionalServiceItemFromOrderItem(
            order_item, self.order, self, ignore_promo_on_invoice
        )

        _, _, name = service_item_dto.get_descriptions()

        return InvoiceItem.objects.create(
            invoice=self,
            order_item=self.get_sotty_items().first(),
            item_type=InvoiceItemType.SERVICE,
            item_name=name,
            quantity=order_item.quantity,
            vat_rate=vat_rate,
            vat_status=vat_status,
            net_price=service_item_dto.net_price_per_item,
            discount_value=service_item_dto.discount_value_per_item,
            net_value=service_item_dto.net_value_total,
            vat_amount=service_item_dto.vat_amount_total,
            gross_price=service_item_dto.gross_price_total,
            recycle_tax_value=Decimal('0.00'),
        )

    def get_vat_type(self):
        if self.order.is_united_kingdom():
            return VatType.EU

        vat_type = VatType.NORMAL
        if self.order.country and self.order.country.lower() in [
            Countries.switzerland.name,
            Countries.norway.name,
        ]:
            vat_type = VatType.SWITZERLAND

        first_order_item = self.order.items.first()
        if first_order_item and first_order_item.vat_amount == 0:
            vat_type = VatType.EU
        return vat_type

    def get_vat_rate(self):
        if self.order.is_united_kingdom():
            # domestic invoice vs normal invoice for uk
            return Decimal(0)
        if self.order.vat_rate is not None:
            return self.order.vat_rate
        vat_details = VatDetailsGetter(
            self.order,
            sell_at=self.sell_at.date() if self.sell_at else None,
            get_vat_type_from_order=True,
        )
        return vat_details.get_vat_rate_for_display()

    def get_numeration_type_and_prefix(self):
        if self.order.is_united_kingdom():
            numeration_type = NumerationType.INV
            prefix = 'INV/'
        # individual client should be issued by region entity
        elif self.order.region_vat and self.order.vat_type == VAT_NORMAL:
            numeration_type = NumerationType.RV
            prefix = 'RV/'
        else:
            numeration_type = NumerationType.NORMAL
            prefix = ''
        return numeration_type, prefix

    def _generate_pretty_id_db_sequence(self, issued_at=None):
        now = timezone.now()
        if issued_at:
            issue_date = issued_at
        elif self.issued_at:
            issue_date = self.issued_at
        else:
            issue_date = now

        numeration_type, prefix = self.get_numeration_type_and_prefix()

        invoice_country = (
            self.order.country if numeration_type != NumerationType.NORMAL else 'poland'
        )
        seq = InvoiceSequence.objects.filter(
            country__name=invoice_country,
            invoice_type=self.status,
            numeration_type=numeration_type,
        )
        check = seq.count()

        if 0 == check or check > 1:
            raise ValueError('Numeration sequence error')

        seq = seq.last()
        mapper = {
            'usa': 'US',
            'japan': 'JPN',
            'monaco': 'MCO',
            'hong kong': 'HKG',
            'canada': 'CAN',
            'kuwait': 'KWT',
            'turkey': 'TUR',
            'australia': 'AUS',
        }
        country = str(self.order.country.lower())
        if country in Countries.__members__:
            country_code = Countries.__members__[country].code
        elif self.order.region.name == OTHER_REGION_NAME and country in mapper:
            country_code = mapper[country]
        else:
            raise ValueError('Not supported country')

        if self.status == InvoiceStatus.CORRECTING:
            prefix += 'FKS'
        elif self.status == InvoiceStatus.ENABLED:
            pass
        elif self.status == InvoiceStatus.ENABLED_VAT_REGION_CORRECTION:
            pass
        elif self.status == InvoiceStatus.PROFORMA:
            prefix += 'PROFORMA'
        else:
            prefix += 'DIFF'

        if now.month == issue_date.month and now.year == issue_date.year:
            with atomic():
                invoice_number = seq.get_next_current_month_sequence()
                num = seq.pretty_id_template.format(
                    **{
                        'invoice_number': invoice_number,
                        'order_id': self.order.id,
                        'country_code': country_code,
                        'issued_date': issue_date.strftime('%m/%Y'),
                        'prefix': prefix,
                    }
                )
                logger.debug(
                    'InvoiceSequence {} {} {} {} {}'.format(
                        num, invoice_number, invoice_country, self.order.id, issue_date
                    )
                )
        else:
            with atomic():
                invoice_number = seq.get_next_chosen_date_sequence(issue_date)
                num = seq.pretty_id_template.format(
                    **{
                        'invoice_number': invoice_number,
                        'order_id': self.order.id,
                        'country_code': country_code,
                        'issued_date': issue_date.strftime('%m/%Y'),
                        'prefix': prefix,
                    }
                )
                logger.debug(
                    'InvoiceSequence --diff year/month -- {} {} {} {} {}'.format(
                        num, invoice_number, invoice_country, self.order.id, issue_date
                    )
                )

        return seq.pretty_id_template.format(
            **{
                'invoice_number': invoice_number,
                'order_id': self.order.id,
                'country_code': country_code,
                'issued_date': issue_date.strftime('%m/%Y'),
                'prefix': prefix,
            }
        )

    def _generate_pretty_id(self, issued_at=None):
        """
        Generates pretty id, new standard after new year
        :return: :class:`string` representing pretty id
        """
        from .legacy_sequences import (
            generate_pretty_id__2017_2018,
            generate_pretty_id__lt_2017,
        )

        prefix = ''
        if issued_at:
            issue_date = issued_at
        elif self.issued_at:
            issue_date = self.issued_at
        else:
            issue_date = timezone.now()
        if issue_date is not None and (
            issue_date.tzinfo is None or issue_date.tzinfo.utcoffset(issue_date) is None
        ):
            issue_date = timezone.make_aware(issue_date)
        if issue_date < timezone.make_aware(datetime(year=2017, month=1, day=1)):
            if self.order is None:
                return '00/000000/00000/00'
            (
                country_code,
                document_type,
                invoice_no,
                project_id,
            ) = generate_pretty_id__lt_2017(self, issue_date, issued_at)
            if self.status == InvoiceStatus.PROFORMA:
                return '{}/{}{}/{}{:d}/{}'.format(
                    document_type,
                    'PROFORMA',
                    issue_date.strftime('%m%y'),
                    project_id,
                    self.order.id,
                    country_code,
                )
            else:
                return '{}/{:02d}{}/{}{:d}/{}'.format(
                    document_type,
                    invoice_no,
                    issue_date.strftime('%m%y'),
                    project_id,
                    self.order.id,
                    country_code,
                )
        elif issue_date < timezone.make_aware(
            datetime(year=2019, month=1, day=1)
        ):  # new way
            if self.order is None:
                return '00000/00/0000/00000/00'  # id/month/year/ordernumber/country
            return generate_pretty_id__2017_2018(self, issue_date, issued_at)
        else:
            if self.order is None:
                return '00000/00/0000/00000/00'  # id/month/year/ordernumber/country

            invoices_from_selected_year_month = (
                Invoice.objects.filter(
                    Q(issued_at__year=issue_date.year)
                    | Q(corrected_issued_at__year=issue_date.year)
                )
                .filter(
                    Q(issued_at__month=issue_date.month)
                    | Q(corrected_issued_at__month=issue_date.month)
                )
                .exclude(pk=self.pk)
                .order_by('issued_at', 'id')
            )
            if self.status in (
                InvoiceStatus.ENABLED,
                InvoiceStatus.CORRECTING,
            ) and issue_date >= timezone.make_aware(
                datetime(year=2019, month=10, day=1)
            ):
                if (
                    self.pretty_id
                    and 'PROFORMA' not in self.pretty_id
                    and 'DRAFT' not in self.pretty_id
                ):
                    raise ValueError('Invoice already have pretty_id')
                inv = Invoice.objects.filter(pk=self.pk).first()
                if (
                    inv
                    and inv.pretty_id
                    and 'PROFORMA' not in self.pretty_id
                    and 'DRAFT' not in self.pretty_id
                ):
                    raise ValueError('Invoice already have pretty_id')
                return self._generate_pretty_id_db_sequence(issued_at=issued_at)
            if self.status == InvoiceStatus.CORRECTING:
                invoices_from_selected_year_month = (
                    invoices_from_selected_year_month.filter(
                        status=InvoiceStatus.CORRECTING
                    )
                )
            elif self.status == InvoiceStatus.CORRECTING_DRAFT:
                invoices_from_selected_year_month = (
                    invoices_from_selected_year_month.filter(
                        status=InvoiceStatus.CORRECTING_DRAFT
                    )
                )
            else:
                invoices_from_selected_year_month = (
                    invoices_from_selected_year_month.filter(
                        status=InvoiceStatus.ENABLED
                    )
                )

            # checking vat type - only normal can have region numeration
            if self.order.region_vat and self.order.vat_type == VAT_NORMAL:
                if self.order.is_united_kingdom():
                    invoices_from_selected_year_month = (
                        invoices_from_selected_year_month.filter(order__region_vat=True)
                        .filter(order__country=self.order.country)
                        .filter(pretty_id__startswith='INV')
                    )
                    prefix = 'INV/'
                else:
                    invoices_from_selected_year_month = (
                        invoices_from_selected_year_month.filter(order__region_vat=True)
                        .filter(order__country=self.order.country)
                        .filter(pretty_id__startswith='RV')
                    )
                    prefix = 'RV/'
            else:
                invoices_from_selected_year_month = (
                    invoices_from_selected_year_month.filter(
                        Q(order__region_vat=False)
                        | Q(Q(order__region_vat=True) & Q(order__vat_type=VAT_EU))
                    )
                )

            last_inv = (
                invoices_from_selected_year_month.exclude(pretty_id='')
                .exclude(pretty_id=None)
                .order_by('pk')
                .last()
            )

            if not last_inv or not str(last_inv.get_invoice_number_in_year()).isdigit():
                invoice_no = 1
            else:
                invoice_no = last_inv.get_invoice_number_in_year() + 1
            try:
                country_code = Countries.__members__[
                    str(self.order.country.lower())
                ].code
            except Exception:
                country_code = '??'
            if self.status == InvoiceStatus.PROFORMA:
                return '{}{}/{}/{:d}/{}'.format(
                    prefix,
                    'PROFORMA',
                    issue_date.strftime('%m/%Y'),
                    self.order.id,
                    country_code,
                )
            elif self.status == InvoiceStatus.CORRECTING:
                return '{}FKS{:4d}/{}/{:d}/{}'.format(
                    prefix,
                    invoice_no,
                    issue_date.strftime('%m/%Y'),
                    self.order.id,
                    country_code,
                )
            elif self.status == InvoiceStatus.CORRECTING_DRAFT:
                return 'DRAFT'
            else:
                return '{}{:5d}/{}/{:d}/{}'.format(
                    prefix,
                    invoice_no,
                    issue_date.strftime('%m/%Y'),
                    self.order.id,
                    country_code,
                )

    def get_previous_correction(self):
        if (
            self.is_correction
            and self.corrected_invoice
            and not self.previous_correction
        ):
            if self.pk:
                self.previous_correction = (
                    self.corrected_invoice.corrections.filter(id__lt=self.pk)
                    .exclude(status=InvoiceStatus.CORRECTING_DRAFT)
                    .last()
                )
            else:
                self.previous_correction = self.corrected_invoice.corrections.exclude(
                    status=InvoiceStatus.CORRECTING_DRAFT
                ).last()
            self.save(update_fields=['previous_correction'])
        return self.previous_correction

    @transaction.atomic
    def save(self, *args, **kwargs):
        """
        Saves Invoice ensuring the pretty id and InvoiceItems.
        """

        # temporary exception for invoices with total value 0, after finding all places
        # and keeping it quiet for few weeks - remove it.
        # If it is here still after 2021.01 and you found it - fix or remove.

        update_fields = kwargs.get('update_fields', [])
        if update_fields == ['cached_to_dict']:
            kwargs.pop('update_fields', None)
            return super().save(*args, **kwargs)

        ignore_discount = kwargs.pop('ignore_discount', False)
        ignore_create_items = kwargs.pop('ignore_create_items', False)
        proforma_invoice_pk = kwargs.pop('proforma_invoice_pk', None)

        try:
            if (
                self.pk is None
                and self.status == InvoiceStatus.ENABLED
                and self.order.total_price == 0
                and proforma_invoice_pk is None
            ):
                self.status = InvoiceStatus.PROFORMA
                raise ValueError(
                    'Invoice for order {} with total value = 0'.format(self.order_id)
                )
        except ValueError as e:
            logger.error(e, exc_info=True)
        if self.pk:
            self.__class__.all_objects.select_for_update().filter(pk=self.pk)
        if self.status == InvoiceStatus.CORRECTING:
            if not (
                'update_fields' in kwargs
                and (
                    'previous_correction' in kwargs['update_fields']
                    or 'vat_amount_in_pln' in kwargs['update_fields']
                )
            ):
                self._validate_correction()

        if 'special' in kwargs:
            kwargs.pop('special')
            super().save(*args, **kwargs)
            history = self._versioning_set_up()
            self._versioning_post_save(history)
            return
        if not self.pretty_id:
            self.pretty_id = self._generate_pretty_id()
            if self.order.different_billing_address:
                self.show_both_address = True
        elif self.pk is not None or proforma_invoice_pk:
            # Invalidate vat calculations on sell date change.
            source_pk = self.pk or proforma_invoice_pk
            version_before_save = self.__class__.all_objects.get(id=source_pk)
            if (
                version_before_save.sell_at.tzinfo is None
                or version_before_save.sell_at.tzinfo.utcoffset(
                    version_before_save.sell_at
                )
                is None
            ):
                date_to_check = timezone.make_aware(version_before_save.sell_at)
            else:
                date_to_check = version_before_save.sell_at
            if (
                self.sell_at.tzinfo is None
                or self.sell_at.tzinfo.utcoffset(self.sell_at) is None
            ):
                sell_at_check = timezone.make_aware(self.sell_at)
            else:
                sell_at_check = self.sell_at
            if date_to_check != sell_at_check:
                self.exchange_date, self.exchange_rate, self.vat_amount_in_pln = (
                    None,
                    None,
                    None,
                )
            if version_before_save.receipt_id != self.receipt_id and not (
                version_before_save.receipt_id is None or self.receipt_id is None
            ):
                self.receipt_date_change = timezone.now()

        if not self.exchange_date or not self.exchange_rate:
            self.exchange_date, self.exchange_rate = self.get_exchange_date_and_rate()
            if isinstance(self.exchange_date, datetime):
                self.exchange_date = self.exchange_date.date()

        if self.status != InvoiceStatus.BLANK and self.order is None:
            logger.error(
                'Trying to create invoice without order! Invoice id %s',
                self.pretty_id,
            )
        else:
            # double check if saving before versioning is ok
            super().save(*args, **kwargs)
            history = self._versioning_set_up()

            self._versioning_post_save(history)
            if self.should_create_items(ignore_create_items=ignore_create_items):
                self._create_items(ignore_discount=ignore_discount)

    def should_create_items(self, ignore_create_items: bool) -> bool:
        if ignore_create_items:
            return False

        valid_status = self.status not in {
            InvoiceStatus.CORRECTING,
            InvoiceStatus.CORRECTING_DRAFT,
            InvoiceStatus.CORRECTING_PROFORMA,
            InvoiceStatus.BLANK,
        }
        return valid_status and (self.pk is None or self.invoice_items.count() == 0)

    def _validate_correction(self):
        """
        Function checking if correction is valid in term of tax law.
        If not it will raise error.
        :return:
        """
        current_country_code = '??'
        mapper = {
            'usa': 'US',
            'japan': 'JPN',
            'monaco': 'MCO',
            'hong kong': 'HKG',
            'canada': 'CAN',
            'kuwait': 'KWT',
            'turkey': 'TUR',
        }

        country = str(self.order.country.lower())
        if country in Countries.__members__:
            current_country_code = Countries.__members__[country].code
        elif self.order.region.name == OTHER_REGION_NAME and country in mapper:
            current_country_code = mapper[country]
        org_country_code = self.corrected_invoice.pretty_id.split('/')[-1]
        if org_country_code == 'GB':
            org_country_code = 'UK'

        if current_country_code != org_country_code:
            raise ValueError(
                'Country code of original invoice is different than correction'
            )

        if force_str(self.currency_symbol) != force_str(
            self.corrected_invoice.currency_symbol
        ):
            raise ValueError(
                'Currency symbol of original invoice is different than correction'
            )

    def _versioning_post_save(self, history):
        """
        Creating InvoiceHistory objects if its needed
        :param history:
        :return:
        """
        if history and not history.invoice_raw_id:
            history.invoice_raw_id = self.pk
        if history:
            history.save()

    def _versioning_set_up(self):
        """
        Setting up InvoiceHistory object.
        :return: None if nothing to save or  InvoiceHistory objects that should be saved
        """

        from invoice.serializers import InvoiceSerializer
        from orders.serializers import OrderFullSerializer

        order_serialized = OrderFullSerializer(self.order)
        history = InvoiceHistory.objects.filter(invoice_raw_id=self.pk).last()
        save_history = False

        if not history:
            history = InvoiceHistory(invoice_raw_id=self.pk)

        # checking order history
        if not self.pk and not history.generated_from_order:
            # new invoice
            history.generated_from_order = [
                {
                    'when': datetime.now().isoformat(),
                    'order': order_serialized.data,
                    'dirty': False,
                },
            ]
            save_history = True
        elif self.pk and not history.generated_from_order:
            # existing invoice, created before versioning
            history.generated_from_order = [
                {
                    'when': datetime.now().isoformat(),
                    'order': order_serialized.data,
                    'dirty': True,
                },
            ]
            save_history = True
        elif self.pk and history.generated_from_order:
            # existing invoice, created after versioning have
            # changes in order after versioning
            prev = json.dumps(history.generated_from_order[0]['order'], sort_keys=True)
            now = json.dumps(order_serialized.data, sort_keys=True)
            if prev != now:
                # checking if there are changes on invoice
                history.generated_from_order.insert(
                    0,
                    {
                        'when': datetime.now().isoformat(),
                        'order': order_serialized.data,
                        'dirty': False,
                    },
                )
                save_history = True
        elif not self.pk and history.generated_from_order:
            # existing invoice, should not have happened
            history.generated_from_order.insert(
                0,
                {
                    'when': datetime.now().isoformat(),
                    'order': order_serialized.data,
                    'dirty': True,
                    'no_pk': True,
                },
            )
            save_history = True
        # checking invoice history
        inv_serialized = InvoiceSerializer(self)

        if not history.invoice_history:

            self.currency_symbol = force_str(self.currency_symbol)
            history.invoice_history = [
                {'when': datetime.now().isoformat(), 'inv': inv_serialized.data},
            ]
            save_history = True
        else:
            before = json.dumps(history.invoice_history[0]['inv'], sort_keys=True)
            now = json.dumps(inv_serialized.data, sort_keys=True)
            if before != now:
                history.invoice_history.insert(
                    0,
                    {
                        'when': datetime.now().isoformat(),
                        'inv': dict(inv_serialized.data),
                    },
                )
                save_history = True
        if save_history:
            return history

    def get_order_no(self):
        """
        Returns number of the order invoice services including
        the number of the invoice in month.
        :return: :class:`string` representing order number
        """
        if not self.pretty_id:
            raise Exception('Fill out the Invoice first')
        return self.pretty_id.split('/')[2]

    def get_exchange_date_and_rate(self) -> tuple[datetime, Decimal]:
        from .utils import first_working_day_before

        if self.force_exchange_date and self.force_exchange_rate:
            return self.force_exchange_date, self.force_exchange_rate
        if self.exchange_date and self.exchange_rate:
            return self.exchange_date, Decimal(self.exchange_rate)

        if self.sell_at is None:
            exchange_date = self.created_at - timedelta(days=1)
        else:
            exchange_date = self.sell_at - timedelta(days=1)

        exchange_date = first_working_day_before(exchange_date)
        currency_rates = ExchangeRate.get_safe_exchange(
            exchange_date.year, exchange_date.month, exchange_date.day
        )

        if len(list(currency_rates.items())) == 0:
            raise ValueError('Missing rates for invoice {}, check it'.format(self.id))

        currency_code = 'EUR'
        if self.order is not None:
            currency_code = (
                self.order.currency.code
                if self.order.currency
                else self.order.get_region(dont_use_cache=True).currency.code
            )
        return (
            exchange_date,
            Decimal(currency_rates[currency_code]).quantize(
                Decimal('.0001'), rounding=ROUND_HALF_UP
            ),
        )

    def get_render_strategy(self):
        if earliest_invoice_with_services_version_not_supported(self.order):
            return ServiceAsSeparateEntryStrategy(self)
        return ServiceCombinedWithItemStrategy(self)

    def create_pdf(self):
        from .internal_api.events import InvoiceGeneratedEvent

        strategy = self.get_render_strategy()
        template_name = strategy.get_template_name()
        try:
            context = strategy.create_context_for_pdf()
            pdf_bytes = render_pdf_from_template(template_name, context)
            pretty_id = self.pretty_id.replace('/', '_')
            uid = str(uuid.uuid4()).replace('-', '_')
            self.pdf.save(
                f'inv_{pretty_id}_{uid}.pdf',
                ContentFile(pdf_bytes),
            )
            self.save(update_fields=['pdf'], special=True)
            if (
                not self.is_domestic
                and self.status not in InvoiceStatus.preview_statuses()
            ):
                InvoiceGeneratedEvent(self.order)
        except CalledProcessError as e:
            logger.exception(
                '[invoice_id=%s] Error while creating pdf: %s %s',
                self.id,
                e.returncode,
                e.output,
            )
            raise e

    def is_total_the_same(self):
        (total_price, total_sum, diff) = self.total_prices_comparision_numbers()
        return total_price == total_sum

    def count_correction_invoice_items(self):
        return self.invoice_items.filter(corrected_invoice_item__isnull=False).count()

    def get_original_invoice(self):
        if self.status != InvoiceStatus.CORRECTING or self.corrected_invoice is None:
            return self
        else:
            return self.corrected_invoice.get_original_invoice()

    def total_prices_comparision_numbers(self):
        invoice_items = dict(
            [(k, []) for k in list(dict(InvoiceItemType.choices).keys())]
        )
        for invoice_item in self.invoice_items.all():
            invoice_items[invoice_item.item_type].append(invoice_item)

        total_gross_price = Decimal(0.00)
        for invoice_item in itertools.chain(*list(invoice_items.values())):
            total_gross_price += invoice_item.gross_price

        if self.order is not None and self.order.get_total_value() is not None:
            diff = Decimal(self.order.get_total_value()) - Decimal(total_gross_price)
            return (self.order.get_total_value(), total_gross_price, diff)
        else:
            return (None, None, None)

    @cache_model_method(cache_period=36000)
    def get_payment_date(self):
        if self.order:
            if self.order.is_united_kingdom_with_samples_only():
                return self.order.paid_at
            transactions = self.order.transactions.filter(status='AUTHORISATION')
            if transactions.count() > 0:
                notifications = transactions.first().notification_set.filter(
                    code='AUTHORISATION'
                )
                if notifications.count() > 0:
                    notification = notifications.first()
                    return notification.event_date
        return None

    def get_payment_form(self):
        from payments.models import Notification  # circular import

        authorisation_status = 'AUTHORISATION'
        possible_notification = (
            Notification.objects.filter(
                code=authorisation_status,
                success=True,
                transaction__status=authorisation_status,
                transaction__order=self.order,
            )
            .order_by('-event_date')
            .first()
        )
        if possible_notification:
            return possible_notification.get_payment_form()
        return 'bank transfer'

    def get_code(self):
        return '{} {}'.format(self.order.first_name, self.order.last_name)

    get_code.short_description = 'Dane z zamowienia'

    def sum_invoice_items_gross_price(self) -> Decimal:
        return self.invoice_items.aggregate(
            gross_price__sum=Coalesce(Sum('gross_price'), Value(Decimal('0.0')))
        )['gross_price__sum']

    def get_exchange_rate(self):
        return Decimal(self.get_exchange_date_and_rate()[1]).quantize(
            Decimal('.0001'), rounding=ROUND_HALF_UP
        )

    get_exchange_rate.short_description = 'Wartosc kursu eur:pln'

    def get_booking_date(self):
        if self.payable_booking_date is not None:
            return self.payable_booking_date
        else:
            return self.order.payable_booking_date

    def get_total_gross(self):
        return sum([x.gross_price for x in self.get_invoice_items()])

    def get_total_net(self):
        return round(sum([x.net_value for x in self.get_invoice_items()]))

    def get_total_net_price(self):
        return sum(item.net_price for item in self.get_invoice_items())

    def get_total_net_weight(self):
        return sum(item.net_weight for item in self.get_invoice_items())

    def get_total_gross_weight(self):
        return sum(item.gross_weight for item in self.get_invoice_items())

    def display_name_with_material(self):
        invoice_items = self.invoice_items.filter(item_type=InvoiceItemType.ITEM).all()
        return '\n'.join([item.display_name_with_material() for item in invoice_items])

    def get_correction_differences(self, use_forced_values=True):
        invoice_dict = self.to_dict(use_forced_values=use_forced_values)
        if invoice_dict['previous_correction']:
            previous_invoice_dict = invoice_dict['previous_correction']
        else:
            previous_invoice_dict = invoice_dict['corrected_invoice']
        vat_rate = self.get_vat_rate()
        key = as_percentage(vat_rate)
        old_values = {
            '0': {'net': 0, 'vat': 0, 'gross': 0},
            '23': {'net': 0, 'vat': 0, 'gross': 0},
            key: {'net': 0, 'vat': 0, 'gross': 0},
            'totals': {
                'net_old': 0,
                'vat_old': 0,
                'gross_old': 0,
                'net': 0,
                'vat': 0,
                'gross': 0,
                'net_diff': 0,
                'vat_diff': 0,
                'gross_diff': 0,
            },
        }
        new_values = deepcopy(old_values)
        is_invoice_for_france = self.order.country == 'france'

        for previous_item in previous_invoice_dict['items']:
            pi_vat_key = key if previous_item['vat_amount'] > 0 else '0'
            old_values[pi_vat_key]['net'] += previous_item['net_value']
            old_values[pi_vat_key]['vat'] += previous_item['vat_amount']
            old_values[pi_vat_key]['gross'] += previous_item['gross_price']
            old_values['totals']['net_old'] += previous_item['net_value']
            old_values['totals']['vat_old'] += previous_item['vat_amount']
            old_values['totals']['gross_old'] += previous_item['gross_price']
            old_values['totals']['net_diff'] += previous_item['net_value_in_pln']
            old_values['totals']['vat_diff'] += previous_item['vat_amount_in_pln']
            old_values['totals']['gross_diff'] += previous_item['gross_price_in_pln']

            if is_invoice_for_france:
                if 'recycle_tax_old' not in old_values['totals']:
                    old_values['totals']['recycle_tax_old'] = 0
                # if no recycle_tax value in cached dict, then it equals to zero
                old_values['totals']['recycle_tax_old'] += previous_item.get(
                    'recycle_tax_value', Decimal('0.0')
                )
        for current_item in invoice_dict['items']:
            ci_vat_key = key if current_item['vat_amount'] > 0 else '0'
            new_values[ci_vat_key]['net'] += current_item['net_value']
            new_values[ci_vat_key]['vat'] += current_item['vat_amount']
            new_values[ci_vat_key]['gross'] += current_item['gross_price']
            new_values['totals']['net'] += current_item['net_value']
            new_values['totals']['vat'] += current_item['vat_amount']
            new_values['totals']['gross'] += current_item['gross_price']
            new_values['totals']['net_diff'] += current_item['net_value_in_pln']
            new_values['totals']['vat_diff'] += current_item['vat_amount_in_pln']
            new_values['totals']['gross_diff'] += current_item['gross_price_in_pln']

            if is_invoice_for_france:
                if 'recycle_tax' not in new_values['totals']:
                    new_values['totals']['recycle_tax'] = 0
                # if no recycle_tax value in cached dict, then it equals to zero
                recycle_tax_value = current_item.get(
                    'recycle_tax_value', Decimal('0.0')
                )
                if (
                    self.order.status == OrderStatus.CANCELLED
                    and not current_item['gross_price']
                    and recycle_tax_value > 0
                ):
                    # This case is applied only when eco fee hasn't been reset
                    # to zero after order cancellation.
                    recycle_tax_value = Decimal('0.0')

                new_values['totals']['recycle_tax'] += recycle_tax_value

        old_forced = False
        if (
            old_values['totals']['gross_old'] != previous_invoice_dict['total_value']
            or old_values['totals']['vat_diff'] != previous_invoice_dict['vat_in_pln']
            or old_values['totals']['vat_old'] != previous_invoice_dict['vat_value']
        ):
            old_forced = True
            pi_vat_key = key if previous_invoice_dict['vat_value'] > 0 else '0'
            old_values[pi_vat_key]['net'] = previous_invoice_dict['net_value']
            old_values[pi_vat_key]['vat'] = previous_invoice_dict['vat_value']
            old_values[pi_vat_key]['gross'] = previous_invoice_dict['total_value']
            old_values['totals']['net_old'] = previous_invoice_dict['net_value']
            old_values['totals']['vat_old'] = previous_invoice_dict['vat_value']
            old_values['totals']['gross_old'] = previous_invoice_dict['total_value']
            old_values['totals']['net_diff'] = previous_invoice_dict['net_value_in_pln']
            old_values['totals']['vat_diff'] = previous_invoice_dict['vat_in_pln']
            old_values['totals']['gross_diff'] = previous_invoice_dict[
                'total_value_in_pln'
            ]

        if new_values['totals']['gross_old'] != invoice_dict['total_value']:
            pi_vat_key = key if invoice_dict['vat_value'] > 0 else '0'
            new_values[pi_vat_key]['net'] = invoice_dict['net_value']
            new_values[pi_vat_key]['vat'] = invoice_dict['vat_value']
            new_values[pi_vat_key]['gross'] = invoice_dict['total_value']
            new_values['totals']['net'] = invoice_dict['net_value']
            new_values['totals']['vat'] = invoice_dict['vat_value']
            new_values['totals']['gross'] = invoice_dict['total_value']
            new_values['totals']['net_diff'] = invoice_dict['net_value_in_pln']
            new_values['totals']['vat_diff'] = invoice_dict['vat_in_pln']
            new_values['totals']['gross_diff'] = invoice_dict['total_value_in_pln']

        resp_dict = {
            '0': {
                'vat': 0,
                'gross': (
                    new_values['0']['gross'] * invoice_dict['exchange_rate']
                ).quantize(Decimal('.01'), rounding=ROUND_HALF_UP)
                - (
                    old_values['0']['gross'] * previous_invoice_dict['exchange_rate']
                ).quantize(Decimal('.01'), rounding=ROUND_HALF_UP),
            },
            key: {
                'gross': (
                    new_values[key]['gross'] * invoice_dict['exchange_rate']
                ).quantize(Decimal('.01'), rounding=ROUND_HALF_UP)
                - (
                    old_values[key]['gross'] * previous_invoice_dict['exchange_rate']
                ).quantize(Decimal('.01'), rounding=ROUND_HALF_UP),
            },
            'totals': {
                'net_old': old_values['totals']['net_old'],
                'vat_old': old_values['totals']['vat_old'],
                'gross_old': old_values['totals']['gross_old'],
                'net': new_values['totals']['net'],
                'vat': new_values['totals']['vat'],
                'gross': new_values['totals']['gross'],
            },
        }
        resp_dict['0']['net'] = resp_dict['0']['gross']
        if old_forced is True:
            resp_dict[key]['vat'] = (
                (
                    old_div(
                        (
                            new_values[key]['gross'] * invoice_dict['exchange_rate']
                        ).quantize(Decimal('.01'), rounding=ROUND_HALF_UP),
                        (vat_rate + 1),
                    )
                )
                * vat_rate
            ).quantize(Decimal('.01'), rounding=ROUND_HALF_UP) - old_values['totals'][
                'vat_diff'
            ]
        else:
            resp_dict[key]['vat'] = (
                (old_div(resp_dict[key]['gross'], (vat_rate + 1))) * vat_rate
            ).quantize(Decimal('.01'), rounding=ROUND_HALF_UP)
        resp_dict[key]['net'] = resp_dict[key]['gross'] - resp_dict[key]['vat']

        if old_values['totals']['vat_old'] > 0:
            resp_dict['totals']['net_old'] = (
                resp_dict['totals']['gross_old'] - resp_dict['totals']['vat_old']
            )
        else:
            resp_dict['totals']['vat_old'] = Decimal(0)

        if new_values['totals']['vat'] > 0:
            resp_dict['totals']['net'] = (
                resp_dict['totals']['gross'] - resp_dict['totals']['vat']
            )
        else:
            resp_dict['totals']['vat'] = Decimal(0)

        if key == '0':
            net_diff = resp_dict['0']['net']
            gross_diff = resp_dict['0']['gross']
            vat_diff = resp_dict['0']['vat']
        else:
            net_diff = resp_dict['0']['net'] + resp_dict[key]['net']
            gross_diff = resp_dict['0']['gross'] + resp_dict[key]['gross']
            vat_diff = resp_dict['0']['vat'] + resp_dict[key]['vat']

        resp_dict['totals']['net_diff'] = net_diff
        resp_dict['totals']['vat_diff'] = vat_diff
        resp_dict['totals']['gross_diff'] = gross_diff

        if is_invoice_for_france:
            resp_dict['totals']['recycle_tax_old'] = old_values['totals'][
                'recycle_tax_old'
            ]
            resp_dict['totals']['recycle_tax'] = new_values['totals']['recycle_tax']
            resp_dict['totals']['correction_recycle_tax'] = (
                resp_dict['totals']['recycle_tax']
                - resp_dict['totals']['recycle_tax_old']
            )

        resp_dict['totals']['correction_net'] = (
            resp_dict['totals']['net'] - resp_dict['totals']['net_old']
        )
        resp_dict['totals']['correction_vat'] = (
            resp_dict['totals']['vat'] - resp_dict['totals']['vat_old']
        )
        resp_dict['totals']['correction_gross'] = (
            resp_dict['totals']['gross'] - resp_dict['totals']['gross_old']
        )

        for k, v in list(resp_dict.items()):
            for k2, v2 in list(resp_dict[k].items()):
                if isinstance(v2, Decimal):
                    resp_dict[k][k2] = v2.quantize(
                        Decimal('.01'), rounding=ROUND_HALF_UP
                    )

        return resp_dict

    def should_display_difference_on_correction(self):
        if self.order.is_poland():
            return False
        if self.order.region_vat:
            return True
        if self.is_domestic:
            return True
        return not self.order.region_vat and self.order.vat_type == VAT_NORMAL

    def has_correction_value_differences(self):
        if self.correction_changes.filter(
            correction_type__in=[
                InvoiceChangeScope.EXCHANGE,
                InvoiceChangeScope.EXCHANGE_DATE,
            ]
        ).exists():
            return True
        return self.invoice_items.filter(corrected_invoice_item__isnull=False).exists()

    def get_corrected_issue_date(self):
        if self.corrected_issued_at and (
            self.corrected_issued_at.tzinfo is None
            or self.corrected_issued_at.tzinfo.utcoffset(self.corrected_issued_at)
            is None
        ):
            return timezone.make_aware(self.corrected_issued_at)
        return self.corrected_issued_at

    def get_invoice_number_in_year(self):
        item = (
            InvoiceCorrectionChange.objects.filter(
                correction_type=InvoiceChangeScope.INVOICE_NUMBER
            )
            .filter(correcting_invoice__corrected_invoice=self)
            .last()
        )
        if item is None:
            if self.status == InvoiceStatus.PROFORMA or (
                self.pretty_id and 'PROFORMA' in self.pretty_id
            ):
                return 0
            else:
                try:
                    return int(
                        self.pretty_id.replace('RV/', '')
                        .split('/')[0]
                        .replace('FKS', '')
                    )
                except IndexError:
                    logger.warning(
                        'Invoice id={} has odd pretty id: {}'.format(
                            self.id, self.pretty_id
                        )
                    )
                    return None
                except ValueError:
                    logger.warning(
                        'Old invoice in new year, check {} {}'.format(
                            self.id, self.pretty_id
                        )
                    )
                    return None
        else:
            try:
                return int(
                    self.pretty_id.replace('RV/', '').split('/')[0].replace('FKS', '')
                )
            except IndexError:
                logger.warning(
                    'Invoice id={} has odd pretty id: {}'.format(
                        self.id, self.pretty_id
                    )
                )
                return None
            except ValueError:
                logger.warning(
                    'Old invoice in new year, check {} {}'.format(
                        self.id, self.pretty_id
                    )
                )
                return None

    def to_diff_dict(self, use_forced_values=True) -> dict | None:
        if self.status not in InvoiceStatus.correcting_statuses():
            return None

        calculated_diff = self.get_correction_differences(
            use_forced_values=use_forced_values
        )
        this_diff = self.to_dict(use_forced_values=use_forced_values)
        totals = calculated_diff['totals']
        diff_dict = {
            'order': this_diff['order'],
            'issue_date': this_diff['issue_date'],
            'pretty_id': this_diff['pretty_id'],
            'sell_date': this_diff['sell_date'],
            'vat_in_pln': totals['vat_diff'],
            'net_value_in_pln': totals['net_diff'],
            'total_value_in_pln': totals['gross_diff'],
            'vat_value': totals['vat'] - totals['vat_old'],
            'net_value': totals['net'] - totals['net_old'],
            'total_value': totals['gross'] - totals['gross_old'],
        }
        if self.order.country == 'france':
            diff_dict['recycle_tax_value'] = (
                totals['recycle_tax'] - totals['recycle_tax_old']
            )
        return diff_dict

    def to_dict(
        self,
        use_forced_values=True,
        is_complaint_reproduction=False,
        force_refresh=False,
    ):
        if self.cached_to_dict and not force_refresh:
            return self.cached_to_dict_decoded

        result = {}
        items = list(self.invoice_items.all())

        for f in (
            'id',
            'status',
            'pretty_id',
            'sell_at',
            'issued_at',
            'due_date_at',
            'delivery_date_at',
            'order',
            'additional_total_text',
            'additional_total_value',
            'additional_address_1',
            'additional_address_2',
            'show_both_address',
            'corrected_notes',
            'use_polish_account_in_pln',
            'additional_top',
            'receipt_id',
        ):
            result[f] = getattr(self, f)
            if issubclass(type(result[f]), models.Model):
                result[f] = result[f].pk
        result['currency_symbol'] = self.get_currency_for_pdf()
        result['different_address'] = self.order.different_billing_address
        result['payment_form'] = self.get_payment_form()
        result['sell_date'] = self.sell_at
        (
            result['exchange_date'],
            result['exchange_rate'],
        ) = self.get_exchange_date_and_rate()
        result['is_company'] = self.order.is_company()
        if use_forced_values and self.force_exchange_rate is not None:
            result['exchange_rate'] = self.force_exchange_rate

        previous_correction = self.get_previous_correction()
        if self.is_correction:
            result['issue_date'] = (
                self.corrected_issued_at if self.corrected_issued_at else self.issued_at
            )
            result['correction_for_pretty_id'] = self.corrected_invoice.pretty_id
            result['correction_for_issued_date'] = self.corrected_issued_at
            result['corrected_invoice'] = self.corrected_invoice.to_dict()
            result['previous_correction'] = (
                previous_correction.to_dict() if previous_correction else None
            )
        else:
            result['issue_date'] = self.issued_at
            result['correction_for_pretty_id'] = None
            result['correction_for_issued_date'] = None
        if self.order.country is None and self.force_outside_eu is True:
            outside_eu = True
        elif (  # BREXIT
            self.order.paid_at
            and self.order.paid_at.year < 2021
            and self.order.logistic_info
            and (
                (
                    (
                        self.order.logistic_info[-1].sent_to_customer
                        and self.order.logistic_info[-1].sent_to_customer.year < 2021
                    )
                    or not self.order.logistic_info[-1].sent_to_customer
                )
                or self.issued_at.year < 2021
            )
        ):
            outside_eu = False
        else:
            outside_eu = (
                self.force_outside_eu is True
                or self.order.get_region(dont_use_cache=True).name == OTHER_REGION_NAME
                or (
                    self.order.country
                    and self.order.country.lower()
                    in [
                        'switzerland',
                        'israel',
                        'norway',
                        'united_kingdom',
                    ]
                )
            )
        if (
            outside_eu
            and self.order.country
            and self.order.country.lower()
            not in (
                'switzerland',
                'norway',
                'united_kingdom',
            )
        ):
            use_dap = True
        else:
            use_dap = False
        result['delivery_address'] = {
            'company_name': self.order.company_name,
            'first_name': self.order.first_name,
            'last_name': self.order.last_name,
            'street_address_1': self.order.street_address_1,
            'street_address_2': self.order.street_address_2,
            'city': self.order.city,
            'postal_code': self.order.postal_code,
            'country': self.order.country,
            'phone': self.order.phone,
            'vat': self.order.vat,
            'use_dap': use_dap,
            'outside_eu': outside_eu,
        }
        result['invoice_address'] = self.order.get_address_data(
            force_outside_eu=outside_eu
        )
        result['vat_status'] = self.get_vat_status()
        result['correction_changes'] = []
        if self.is_correction:
            for change in self.correction_changes.all():
                result['correction_changes'].append(
                    {
                        'name': change.name,
                        'previous_state': change.previous_state,
                        'current_state': change.current_state,
                    }
                )
        result['items'] = []
        if self.status not in InvoiceStatus.correcting_statuses():
            for item in items:
                result['items'].append(
                    item.to_dict(is_complaint_reproduction=is_complaint_reproduction)
                )
        else:
            if previous_correction is None:
                previous_items = [
                    invoice_item.to_dict()
                    for invoice_item in self.corrected_invoice.invoice_items.all()
                ]
            else:
                previous_items = previous_correction.to_dict()['items']
            for correcting_item in [item.to_dict() for item in items]:
                for pi in previous_items:
                    to_be_changed = next(
                        (
                            pi
                            for pi in previous_items
                            if correcting_item['corrected_invoice_item']
                            and pi['id']
                            == correcting_item['corrected_invoice_item']['id']
                        ),
                        None,
                    )
                    if to_be_changed:
                        previous_items[
                            previous_items.index(to_be_changed)
                        ] = correcting_item
                if self.should_add_assembly_service_item(
                    correcting_item, previous_items
                ):
                    previous_items.append(correcting_item)

            for previous_item in previous_items:
                result['items'].append(previous_item)
        (
            result['promo_amount'],
            result['net_value'],
            result['vat_value'],
            result['recycle_tax_value'],
            result['total_value'],
        ) = (0, 0, 0, 0, 0)
        # Recalculating prices according to the current (invoice) exchange
        for item in result['items']:
            result['promo_amount'] += item['discount_value']
            result['net_value'] += item['net_value']
            result['vat_value'] += item['vat_amount']
            result['total_value'] += item['gross_price']
            result['recycle_tax_value'] += item.get('recycle_tax_value', Decimal('0.0'))

        vat_rate = self.get_vat_rate()
        if items:
            if items[0].vat_rate is not None:
                vat_rate = items[0].vat_rate

        if result['vat_value'] > 0:
            result['vat_value'] = (
                (old_div(result['total_value'], (vat_rate + 1))) * vat_rate
            ).quantize(Decimal('.01'), rounding=ROUND_HALF_UP)
            result['vat_rate'] = float(vat_rate)
        else:
            result['vat_value'] = 0
            result['vat_rate'] = item['vat_rate'] if len(result['items']) > 0 else 0

        # FIXME: last not null in 2019 - maybe we can remove this
        if use_forced_values and self.force_gross is not None:
            result['total_value'] = self.force_gross
        if use_forced_values and self.force_vat is not None:
            result['vat_value'] = self.force_vat

        result['total_value_before_discount'] = (
            result['net_value'] + result['promo_amount']
        ) * Decimal(1 + result['vat_rate'])

        result['net_value'] = result['total_value'] - result['vat_value']
        # FIXME: last not null in 2019 - maybe we can remove this
        if use_forced_values and self.force_net is not None:
            result['net_value'] = self.force_net

        if 'PLN' in self.currency_symbol:
            result['vat_in_pln'] = result['vat_value']
            result['total_value_in_pln'] = result['total_value']

            result['net_value_in_pln'] = (
                result['total_value_in_pln'] - result['vat_in_pln']
            )
            result['total_value_before_discount_in_pln'] = result[
                'total_value_before_discount'
            ]

            result['promo_amount_in_pln'] = result['promo_amount']
            result['exchange_rate'] = Decimal(1)

        else:
            result['total_value_in_pln'] = (
                result['total_value'] * result['exchange_rate']
            ).quantize(Decimal('.01'), rounding=ROUND_HALF_UP)
            if result['vat_value'] > 0:
                result['vat_in_pln'] = (
                    (old_div(result['total_value_in_pln'], (vat_rate + 1))) * vat_rate
                ).quantize(Decimal('.01'), rounding=ROUND_HALF_UP)
            else:
                result['vat_in_pln'] = result['vat_value']

            result['net_value_in_pln'] = (
                result['total_value_in_pln'] - result['vat_in_pln']
            )
            result['total_value_before_discount_in_pln'] = (
                result['total_value_before_discount'] * result['exchange_rate']
            ).quantize(Decimal('.01'), rounding=ROUND_HALF_UP)

            result['promo_amount_in_pln'] = (
                result['total_value_before_discount_in_pln']
                - result['total_value_in_pln']
            )

        if use_forced_values and self.force_gross_in_pln is not None:
            result['total_value_in_pln'] = self.force_gross_in_pln

        if use_forced_values and self.force_vat_in_pln is not None:
            result['vat_in_pln'] = self.force_vat_in_pln

        if use_forced_values and self.force_net_in_pln is not None:
            result['net_value_in_pln'] = self.force_net_in_pln

        if not self.vat_amount_in_pln:
            self.vat_amount_in_pln = result['vat_in_pln']
            self.save(update_fields=['vat_amount_in_pln'])

        self.cached_to_dict = json.dumps(result, cls=TypedJSONEncoder)
        self.cached_delivery_address_country = result['delivery_address']['country']
        self.save(update_fields=['cached_to_dict', 'cached_delivery_address_country'])
        return result

    def get_currency_for_pdf(self):
        if self.currency_symbol == 'kr':
            currency = self.order.get_region().currency
            return currency.code
        return self.currency_symbol

    @property
    def cached_to_dict_decoded(self):
        return json.loads(self.cached_to_dict, object_hook=decode_typed_strings)

    def should_add_assembly_service_item(self, correcting_item, previous_items):
        return (
            correcting_item not in previous_items
            and correcting_item['corrected_invoice_item'] is None
            and correcting_item['item_type'] == InvoiceItemType.ASSEMBLY
        )

    def get_delivery_date(self):
        delivery_dates = self.order.product_set.all().values_list(
            'delivered_at', flat=True
        )
        if len(delivery_dates) == 0 or len(delivery_dates) != len(
            [_f for _f in delivery_dates if _f]
        ):
            return 'missing'
        return sorted(delivery_dates)[-1].strftime('%d/%m/%Y')

    def _get_date_from_logistic_order(self, field, html=True):
        delivery_dates = (
            getattr(logistic_order, field)
            for logistic_order in sorted(self.order.logistic_info, key=attrgetter('id'))
        )
        dates = [
            date.strftime('%d/%m/%Y') if date else 'missing' for date in delivery_dates
        ]
        if html:
            return mark_safe('</br>:'.join(dates))
        return dates

    @cache_model_method(cache_period=36000)
    def get_package_weight_from_logistic_order(self):
        # List is used to avoid unnecessary db calls for already prefetched objects
        logistic_orders = self.order.logistic_info
        if not logistic_orders:
            return
        weight = Decimal('0.00')
        for logistic_order in logistic_orders:
            weight += Decimal(logistic_order.total_netto_weight).quantize(
                Decimal('.01')
            )
        return weight

    @cache_model_method(cache_period=36000)
    def get_delivery_date_from_logistic_order(self, html=True):
        return self._get_date_from_logistic_order(field='delivered_date', html=html)

    @cache_model_method(cache_period=36000)
    def get_sent_to_customer_from_logistic_order(self, html=True):
        return self._get_date_from_logistic_order(field='sent_to_customer', html=html)

    def get_promo_amount_number(self):
        return self.order.region_promo_amount or sum(
            item.discount_value for item in self.invoice_items.all()
        )

    def get_absolute_url(self):
        return '/admin/invoice/invoice/{}/'.format(self.id)

    def get_vat_status(self):
        item = next(iter(self.invoice_items.all()), None)
        if not item:
            return VatType.NORMAL
        return item.vat_status

    @property
    def mail_template(self):
        from customer_service.models import CSCorrectionRequest

        correction_request = CSCorrectionRequest.objects.filter(
            correction_invoice=self
        ).first()
        if correction_request:
            if self._is_free_return_correction(correction_request):
                return self._get_free_return_mail_template()

        return self._get_default_mail_template()

    @staticmethod
    def _is_free_return_correction(correction_request):
        return (
            hasattr(correction_request, 'free_return')
            and correction_request.type_cs
            in (
                CSCorrectionRequestType.TYPE_NEUTRALIZATION,
                CSCorrectionRequestType.TYPE_FREE_RETURN,
            )
            and correction_request.tag == InvoiceItemTag.FREE_RETURN.value
        )

    def _get_free_return_mail_template(self):
        if self.order.is_klarna_payment():
            return OrderCorrectionInvoiceFreeReturnKlarnaMail
        return OrderCorrectionInvoiceFreeReturnNormalMail

    def get_invoice_mail_template_for_payment(self):
        if self.order.is_klarna_payment():
            return OrderInvoiceKlarnaMail
        return OrderInvoiceMail

    def _get_default_mail_template(self):
        templates = {
            InvoiceStatus.CORRECTING: OrderCorrectionInvoiceMail,
            InvoiceStatus.PROFORMA: OrderInvoiceProformaMail,
            InvoiceStatus.ENABLED: self.get_invoice_mail_template_for_payment(),
        }
        return templates.get(self.status, OrderInvoiceMail)

    def send_invoice(self, order):
        if self.is_ready_to_send(order):
            self.create_pdf()
            self.send_invoice_to_user()

    def send_invoice_to_user(self) -> None:
        if (
            self.order.earliest_invoice_domestic_version_supported()
            and not self.is_domestic
        ):
            return

        self._send_invoice_email()

        InvoiceReadyEvent(
            user=self.order.owner,
            order_id=self.order.id,
            email=self.order.email,
            invoice_url=self.pdf.url,
            invoice_type=map_invoice_status_to_type(self.status),
            is_klarna=self.order.is_klarna_payment(),
        )

        self.__class__.objects.filter(id=self.id).update(sent_invoice_at=timezone.now())

        if self.status == InvoiceStatus.ENABLED:
            self.order.sent_invoice_date = timezone.now()
            self.order.save(update_fields=['sent_invoice_date'])

    def _get_free_return_items(self):
        from customer_service.models import CSCorrectionRequest

        correction_request = CSCorrectionRequest.objects.filter(
            correction_invoice=self
        ).first()
        if correction_request:
            if self._is_free_return_correction(correction_request):
                return ', '.join(
                    str(item_id)
                    for item_id in self.order.items.filter(
                        free_return=correction_request.free_return
                    ).values_list('id', flat=True)
                )
        return ''

    def _send_invoice_email(self):
        bcc_backup_email_list = [
            email_address for _, email_address in settings.INVOICE_BACKUP_RECIPIENTS
        ]
        mail = self.mail_template(
            self.order.email,
            {
                'order': self.order,
                'invoice': self,
                'user': self.order.owner.first_name,
                'order_items_id': self._get_free_return_items(),
            },
            files_to_add=[
                self.pdf,
            ],
            topic_variables={
                'order': self.order.order_pretty_id,
                'order_pretty_id': self.order.order_pretty_id,
            },
            bcc_list=bcc_backup_email_list,
        )
        mail.send(self.order.owner.profile.language)

    @property
    def pending_correction_requests(self):
        return self.cscorrectionrequest_set.filter(
            status=CSCorrectionRequestStatus.STATUS_NEW
        )

    # @cache_model_method(cache_period=3600)
    def correction_info(self):
        correctable_statuses = {
            InvoiceStatus.ENABLED,
            InvoiceStatus.CORRECTING,
            InvoiceStatus.ENABLED_VAT_REGION_CORRECTION,
        }
        if self.status in correctable_statuses:
            response = []
            for inv in self.corrections.all():
                if inv.status == InvoiceStatus.CORRECTING:
                    invoice_link = (
                        f'<a href="/admin/invoice/invoice/{inv.id}/">'
                        f'{inv.pretty_id}</a>'
                    )
                    if inv.pdf:
                        invoice_link += f'&nbsp;&nbsp;<a href="{inv.pdf.url}">pdf</a> '
                    response.append(invoice_link)
            if len(response) == 0:
                return '-'
            return '</br>'.join(response)
        return '-'

    def proforma_sum_items(self):
        sum_net, sum_gross, sum_vat = (0, 0, 0)
        for item in self.invoice_items.all():
            sum_net += item.net_price
            sum_gross += item.gross_price
            sum_vat += item.vat_amount
        self.net_value = sum_net
        self.vat_value = sum_vat
        self.total_value = sum_gross

    def get_international_bank_account_number(self):
        cached_invoice = self.to_dict()
        if cached_invoice['use_polish_account_in_pln']:
            return DEFAULT_IBAN
        return CURRENCY_SYMBOL_TO_IBAN_NUMBER.get(
            cached_invoice['currency_symbol'], DEFAULT_IBAN
        )

    def is_ready_to_send(self, order):
        total_order, total_sum, diff = self.total_prices_comparision_numbers()
        if (
            not self.is_domestic
            and not self.is_total_the_same()
            and order.region_promo_amount != diff
        ):
            logger.error(
                'Check order %d invoice %d, rounding error '
                'total=%s total_sum=%s diff=%s promo=%s',
                order.id,
                self.id,
                total_order,
                total_sum,
                diff,
                order.region_promo_amount,
            )
            return False
        return True

    @property
    def has_pending_klarna_adjustment(self):
        return self.adjustments.filter(finished_at__isnull=True).exists()


class InvoiceDomestic(Invoice):
    objects = InvoiceDomesticManager()
    all_objects = InvoiceDomesticAllManager()

    class Meta:
        proxy = True

    def get_vat_type(self):
        return VatType.NORMAL

    def get_vat_rate(self):
        country = self.order.region.country
        return country.get_country_region_vat_for_sell_at_date(
            self.sell_at or date.today()
        )


class InvoicePreview(Invoice):
    objects = InvoicePreviewManager()
    all_objects = InvoicePreviewAllManager()

    order_items = None

    def __init__(self, *args, **kwargs):
        self.order_items = kwargs.pop('order_items', None)
        super().__init__(*args, **kwargs)

    def get_invoice_items(self):
        return self.invoice_items.filter(order_item__in=self.order_items)

    def get_order_items(self):
        return sorted(self.order_items, key=attrgetter('price'))

    @classmethod
    def from_invoice(cls, invoice, order_items):
        invoice_preview = cls.all_objects.get(pk=invoice.pk)
        invoice_preview.order_items = order_items
        return invoice_preview

    class Meta:
        proxy = True


class InvoiceItem(models.Model):
    """
    Represents an entry on the invoice, eg. :class:`OrderItem`, Delivery, Assembly
    """

    invoice = models.ForeignKey(
        Invoice,
        related_name='invoice_items',
        on_delete=models.CASCADE,
    )
    # :class:`Invoice` the item is part of
    item_type = models.PositiveSmallIntegerField(choices=InvoiceItemType.choices)
    # Invoice item type: Item, Delivery, Assembly
    item_name = models.CharField(max_length=128)
    # Name of the item ordered
    item_material = models.CharField(max_length=128, blank=True, null=True)
    # Material the ordered item is made of
    item_dimensions = models.CharField(max_length=128, blank=True, null=True)
    # Dimensions of the ordered item
    quantity = models.PositiveSmallIntegerField()
    # Amount of the item of this type on the invoice

    # NOTE: all prices here below are in regional currency
    vat_rate = models.DecimalField(max_digits=6, decimal_places=4, default=0.23)
    # VAT rate as decimal
    net_price = models.DecimalField(max_digits=16, decimal_places=2)
    # Net price of the ordered item
    discount_value = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    # Discount value
    net_value = models.DecimalField(max_digits=16, decimal_places=2)
    # Net value after discount
    vat_amount = models.DecimalField(max_digits=16, decimal_places=2)
    # VAT amount (after discount if discount applied)
    gross_price = models.DecimalField(max_digits=16, decimal_places=2)
    # Gross price after discount
    vat_status = models.PositiveSmallIntegerField(
        choices=VatType.choices, default=VatType.NORMAL
    )
    # VAT type/status as explanation behind the vat rate
    recycle_tax_value = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
    )
    net_weight = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    gross_weight = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    order_item = models.ForeignKey(
        'orders.OrderItem',
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )
    tag = models.IntegerField(
        choices=InvoiceItemTag.choices(),
        default=None,
        null=True,
        blank=True,
    )
    discount_tag = models.CharField(
        max_length=60,
        default='',
        blank=True,
    )
    corrected_invoice_item = models.ForeignKey(
        'InvoiceItem',
        blank=True,
        null=True,
        related_name='correcting_items',
        on_delete=models.SET_NULL,
    )

    def __str__(self):
        return '{} {}{} [id={} invoice={}]'.format(
            self.item_name,
            self.gross_price,
            self.invoice.currency_symbol,
            self.pk,
            self.invoice.pretty_id,
        )

    def generate_data_for_symfonia_export(self):
        position = dict()
        position['opis'] = '{0} Order {1}'.format(self.item_name, self.invoice.order.id)
        position['jm'] = 'szt'
        position['jmwp'] = 'szt'
        position['ilosc'] = self.quantity
        position['iloscwp'] = self.quantity
        position['cenawal'] = self.gross_price
        position['cena'] = self.gross_price
        position['wartVat'] = 0 if self.vat_amount == 0 else self.vat_amount
        position['stawkaVAT'] = '{0}%'.format(self.vat_rate * 100)
        return position

    def save(self, *args, **kwargs):
        if (
            self.vat_status == VatType.NORMAL
            and self.vat_amount == 0
            and self.gross_price != 0
        ) or (self.vat_status != VatType.NORMAL and self.vat_amount != 0):
            logger.warning(
                '[invoice_id=%s] %s has %s but vat_amount=%s',
                self.invoice_id,
                self,
                self.get_vat_status_display(),
                self.vat_amount,
            )
        super(InvoiceItem, self).save(*args, **kwargs)

    def get_order_country(self):
        return str(self.invoice.order.get_country())

    get_order_country.short_description = 'Kraj'

    def get_company_name(self):
        return (
            '{} {}'.format(
                self.invoice.order.company_name, self.invoice.order.invoice_company_name
            )
            if self.invoice.order.company_name
            or self.invoice.order.invoice_company_name
            or self.invoice.order.vat
            or self.invoice.order.invoice_vat
            else 'Os. Fizyczna'
        )

    get_company_name.short_description = 'Nazwa firmy'

    def get_exchange_date_and_rate(self) -> tuple[datetime, dict[str, Decimal]]:
        exchange_date = (
            self.invoice.created_at - timedelta(days=1)
            if self.invoice.sell_at is None
            else self.invoice.sell_at - timedelta(days=1)
        )
        if exchange_date.weekday() == 6:
            exchange_date -= timedelta(days=2)
        elif exchange_date.weekday() == 5:
            exchange_date -= timedelta(days=1)

        currency_rates = ExchangeRate.get_safe_exchange(
            exchange_date.year, exchange_date.month, exchange_date.day
        )

        return (exchange_date, currency_rates)

    def get_exchange_date(self):
        return self.get_exchange_date_and_rate()[0]

    get_exchange_date.short_description = 'Data kursu eur:pln'

    def to_dict(self, is_complaint_reproduction=False):
        item_dict = {}
        exchange_date, exchange_rate = self.invoice.get_exchange_date_and_rate()
        for field in [
            'id',
            'item_name',
            'quantity',
            'net_price',
            'discount_value',
            'vat_status',
            'net_value',
            'vat_rate',
            'vat_amount',
            'net_weight',
            'gross_weight',
            'gross_price',
            'recycle_tax_value',
            'item_type',
            'item_material',
            'item_dimensions',
            'order_item_id',
        ]:
            if field == 'item_name' and is_complaint_reproduction:
                item_dict[field] = 'Tylko reklamacja / Tylko replacement'
            elif (
                field in ['net_price', 'net_value', 'gross_price']
                and is_complaint_reproduction
            ):
                complaint = self.invoice.order.get_complaint_related()
                item_dict[
                    field
                ] = complaint.complaint_costs.get_complaint_cost_in_region_currency()
            else:
                item_dict[field] = getattr(self, field)

            if issubclass(type(item_dict[field]), models.Model):
                item_dict[field] = item_dict[field].pk
        item_dict['corrected_invoice_item'] = (
            self.corrected_invoice_item.to_dict()
            if self.corrected_invoice_item
            else None
        )
        item_dict['correcting_invoice_item_ids'] = [
            ci.id for ci in self.correcting_items.all()
        ]
        item_dict['net_price_in_pln'] = (
            item_dict['net_price'] * exchange_rate
        ).quantize(Decimal('.01'), rounding=ROUND_HALF_UP)
        item_dict['discount_value_in_pln'] = (
            item_dict['discount_value'] * exchange_rate
        ).quantize(Decimal('.01'), rounding=ROUND_HALF_UP)

        item_dict['gross_price_in_pln'] = (
            item_dict['gross_price'] * exchange_rate
        ).quantize(Decimal('.01'), rounding=ROUND_HALF_UP)
        vat_rate = self.invoice.get_vat_rate()
        if item_dict['vat_amount'] > 0:
            item_dict['vat_amount_in_pln'] = (
                (old_div(item_dict['gross_price_in_pln'], (vat_rate + 1))) * vat_rate
            ).quantize(Decimal('.01'), rounding=ROUND_HALF_UP)
        else:
            item_dict['vat_amount_in_pln'] = item_dict['vat_amount']
        item_dict['net_value_in_pln'] = (
            item_dict['gross_price_in_pln'] - item_dict['vat_amount_in_pln']
        )
        item_dict['exchange_date'], item_dict['exchange_rate'] = (
            exchange_date,
            exchange_rate,
        )
        item_dict['hts_code'] = self.get_hts_code()
        return item_dict

    def display_name_with_material(self):
        return f'{self.item_name}, {self.item_material}'

    def get_hts_code(self):
        if not self.order_item or self.item_type != InvoiceItemType.ITEM:
            return ''
        furniture_type = self.order_item.get_furniture_type()
        return HTS_CODE_BY_FURNITURE_TYPE.get(furniture_type, HTS_CODE)

    def _get_cached_invoice_items(self) -> list[dict]:
        cached_invoice = self.invoice.to_dict()
        return cached_invoice.get('items', [])

    def _get_cached_invoice_item(self) -> dict:
        cached_item = next(
            (
                invoice_item
                for invoice_item in self._get_cached_invoice_items()
                if invoice_item['id'] == self.id
            ),
        )
        return cached_item

    def get_net_price(self) -> Decimal:
        if not self.invoice.order.is_to_be_shipped_complaint():
            return self.net_price
        cached_item = self._get_cached_invoice_item()
        return cached_item['net_price']

    def get_net_value(self) -> Decimal:
        if not self.invoice.order.is_to_be_shipped_complaint():
            return self.net_value
        cached_item = self._get_cached_invoice_item()
        return cached_item['net_value']

    def get_gross_price(self) -> Decimal:
        if not self.invoice.order.is_to_be_shipped_complaint():
            return self.gross_price
        cached_item = self._get_cached_invoice_item()
        return cached_item['gross_price']


class ProformaCstmInvoice(Invoice):
    objects = ProformaCstmInvoiceManager()

    class Meta:
        proxy = True


class ProformaLogisticInvoice(Invoice):
    objects = ProformaLogisticInvoiceManager()

    class Meta:
        proxy = True


class ProformaLogisticFreeMaterialSampleOutsideEUInvoice(Invoice):
    INVOICE_ITEM_FROM_ORDER_CLASS = FreeSampleInvoiceItemFromOrderItem

    objects = ProformaLogisticFreeMaterialSampleOutsideEUInvoiceManager()

    class Meta:
        proxy = True


class ProformaRetoolInvoice(Invoice):
    objects = ProformaRetoolServiceInvoiceManager()

    class Meta:
        proxy = True


class ProformaCustomerServiceInvoice(Invoice):
    objects = ProformaCustomerServiceInvoiceManager()

    class Meta:
        proxy = True


class InvoiceCorrectionChange(models.Model):
    correcting_invoice = models.ForeignKey(
        'Invoice',
        related_name='correction_changes',
        on_delete=models.CASCADE,
    )
    correction_type = models.PositiveSmallIntegerField(
        choices=InvoiceChangeScope.choices,
    )
    name = models.CharField(max_length=128)
    previous_state = models.CharField(max_length=512)
    current_state = models.CharField(max_length=512)

    def __str__(self):
        return (
            f'InvoiceCorrectionChange[name="{self.name}" '
            f'previous_state="{self.previous_state}" '
            f'current_state="{self.current_state}"]'
        )


class SymfoniaFConfiguration(models.Model):
    document_type = models.CharField(max_length=128)
    account = models.CharField(max_length=128)
    vat_account = models.CharField(max_length=128, blank=True)
    transaction_type = models.CharField(max_length=128)


class InvoiceHistory(models.Model):
    invoice_raw_id = models.IntegerField()
    generated_from_order = models.JSONField(null=True)
    invoice_history = models.JSONField(null=True, blank=True)


class InvoiceSequence(models.Model):
    country = models.ForeignKey(Country, on_delete=models.CASCADE)
    numeration_type = models.IntegerField(choices=NumerationType.choices)
    invoice_type = models.IntegerField(choices=InvoiceStatus.choices)
    pretty_id_template = models.CharField(max_length=100)
    sequence_prefix = models.TextField(blank=True, default=None, null=True)

    def get_next_current_month_sequence(self):
        if self.sequence_prefix:
            with connection.cursor() as cursor:
                cursor.execute('select nextval(%s)', [self._get_sequence_name()])
                result = cursor.fetchone()
            return result[0]
        else:
            raise ValueError('No sequence prefix')

    def get_next_chosen_date_sequence(self, date):
        """
        :param date - issue at date:
        :return:
        """
        if self.sequence_prefix:
            self._create_sequence(date=date)
            self.save()
            with connection.cursor() as cursor:
                cursor.execute('select nextval(%s)', [self._get_sequence_name(date)])
                result = cursor.fetchone()
            return result[0]
        else:
            raise ValueError('No sequence prefix')

    def _create_sequence(self, date=None):
        if not self.sequence_prefix:
            self.sequence_prefix = self._get_sequence_name(date=date)
        with connection.cursor() as cursor:
            seq = self._get_sequence_name(date=date)
            cursor.execute('CREATE SEQUENCE IF NOT EXISTS {}'.format(seq))
            return True

    def _get_sequence_name(self, date=None):
        if not date:
            date = datetime.today()
        month = date.month
        year = date.year
        self.sequence_prefix = '_invoice_{}_{}_{}'.format(
            self.country.name, self.numeration_type, self.invoice_type
        )
        seq = '{}_{}_{}'.format(self.sequence_prefix, year, month)
        return seq

    def __init__(self, *args, **kwargs):
        super(InvoiceSequence, self).__init__(*args, **kwargs)
        if self.pk:
            self._create_sequence()
            self.save()

    def save(self, *args, **kwargs):
        if not self.pk:
            self._create_sequence()
        super(InvoiceSequence, self).save(*args, **kwargs)
