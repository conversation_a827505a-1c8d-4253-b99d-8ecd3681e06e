import datetime
import itertools
import json
import logging
import typing

from collections import defaultdict
from decimal import Decimal
from operator import itemgetter
from typing import Union

from django.conf import settings
from django.contrib import admin
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.core.exceptions import ObjectDoesNotExist
from django.db import (
    IntegrityError,
    models,
)
from django.db.models.query_utils import Q
from django.template.defaultfilters import safe
from django.utils import (
    timezone,
    translation,
)
from django.utils.html import (
    escape,
    format_html,
)

from safedelete.models import SafeDeleteModel

from custom.enums import (
    Furniture,
    LanguageEnum,
    PhysicalProductVersion,
    ShelfType,
)
from custom.helpers import add_business_days
from custom.models import Countries
from custom.utils.decorators import cache_model_method
from custom.utils.emails import send_html_mail
from custom.utils.import_object import import_object
from gallery.enums import ConfiguratorTypeEnum
from gallery.ivy_elements import JettySerialized
from gallery.serializers import furniture_serializer_class_factory
from logger.models import (
    Log,
    LoggerMixin,
)
from orders.enums import (
    OrderStatus,
    OrderType,
)
from producers.choices import (
    AccessLevel,
    BatchNeededActions,
    BatchStatus,
    BatchType,
    DeliveryPriority,
    ProductionFileStatus,
    ProductPriority,
    ProductStatus,
    QualityPriorityChoices,
    QualityResultChoices,
    SourcePriority,
)
from producers.constants import (
    COMPLAINT_DELAY_DAYS_LIMIT,
    OTHER_BATCH_TYPE_STR,
    PRODUCT_DELAY_DAYS_LIMIT,
)
from producers.errors import SerializationMissingError
from producers.managers import (
    BatchingSettingsManager,
    ProductBatchComplaintManager,
    ProductComplaintManager,
    ProductManager,
    ProductPriorityHistoryQuerySet,
    QualityHoldReleaseRequestManager,
)
from producers.models_production_files import ProductBatchJettyProductionFiles
from producers.models_split.batch_actions_needed_flow import BatchActionsNeededFlow
from producers.models_split.details_files_versioning import BatchFilesStatus
from producers.product_updater import (
    ProductSerializationUpdater,
    ProductStatusUpdater,
)
from producers.production_system_utils.client import ProductionSystemClientMixin
from producers.services.product_priority_updater import ProductPriorityUpdater

if typing.TYPE_CHECKING:
    from complaints.models import Complaint

MANUFACTURERS = dict([('unknown', -1), ('drewtur', 1), ('meblepl', 24), ('novum', 25)])

logger = logging.getLogger('producers')
User = get_user_model()


class ActiveManufactor(models.Manager):
    def get_queryset(self):
        return (
            super(ActiveManufactor, self).get_queryset().filter(active_production=True)
        )


class Manufactor(models.Model):
    MEBLE_PL = 'Meblepl'
    SAMPLEBOX = 'SampleBox'
    DREWTUR = 'Drewtur'
    INEX = 'Inex'
    AMIR = 'Amir'
    CENTERMEBEL = 'CenterMebel'
    NOVUM = 'Novum'
    S93 = 'S93'
    GALA = 'Gala'  # FIXME: update after integration with Gala
    TELMEX = 'Telmex'

    SHORT_NAME_IN_CODENAMES = {
        MEBLE_PL: 'MPL',
        DREWTUR: 'DTR',
        INEX: 'INX',
        AMIR: 'AMI',
        NOVUM: 'NOV',
        S93: 'S93',
        TELMEX: 'TLX',
        CENTERMEBEL: 'CML',
        GALA: 'GAL',
    }

    owner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
    )
    name = models.CharField(max_length=250)
    invoice_company_name = models.CharField(
        'invoice company name',
        max_length=256,
        blank=True,
        default='',
    )
    invoice_tax_id = models.CharField(
        'invoice tax id (vat)',
        max_length=256,
        blank=True,
        default='',
    )
    invoice_street_address_1 = models.CharField(
        'invoice street address 1', max_length=256
    )
    invoice_street_address_2 = models.CharField(
        'invoice street address 2',
        max_length=256,
        blank=True,
        default='',
    )
    invoice_city = models.CharField('invoice city', max_length=256)
    invoice_postal_code = models.CharField('invoice postal code', max_length=20)
    invoice_country = models.CharField(
        'invoice country', max_length=50, default='Germany'
    )
    invoice_country_area = models.CharField(
        'invoice country administrative area',
        max_length=128,
        blank=True,
        default='',
    )
    phone = models.CharField('phone number', max_length=30, blank=True, default='')
    latitude = models.DecimalField(
        max_digits=6, decimal_places=3, null=True, blank=True
    )
    longitude = models.DecimalField(
        max_digits=6, decimal_places=3, null=True, blank=True
    )
    logistic_additional_info = models.CharField(max_length=80, default='', blank=True)

    active_production = models.BooleanField(default=False)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = ActiveManufactor()
    all_objects = models.Manager()

    MANUFACTURERS_API_INTEGRATED = [INEX, DREWTUR, S93, GALA]

    class Meta:
        verbose_name = 'Producer entry'
        verbose_name_plural = 'Producer'

    def __str__(self):
        return self.name

    def get_logistic_address(self):
        return f'{self.get_logistic_address_cmr()} </br>{self.logistic_additional_info}'

    def get_logistic_address_cmr(self):
        return (
            f'{self.invoice_company_name}, {self.invoice_street_address_1} '
            f'{self.invoice_postal_code} {self.invoice_city}'
        )

    @classmethod
    def get_s93_manufactor(cls) -> 'Manufactor':
        return Manufactor.objects.get(name=cls.S93)

    @classmethod
    def get_gala_manufactor(cls) -> 'Manufactor':
        return Manufactor.objects.get(name=cls.GALA)

    @property
    def is_gala(self):
        return self.name == self.GALA


class BatchManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().select_related('manufactor')


class ProductBatch(
    models.Model,
    ProductBatchJettyProductionFiles,
    BatchFilesStatus,
):
    manufactor = models.ForeignKey(
        Manufactor,
        null=True,
        blank=True,
        related_name='batches',
        on_delete=models.CASCADE,
    )

    processing_started_at = models.DateTimeField(null=True, blank=True)
    planed_ready_at = models.DateTimeField(null=True, blank=True)
    picked_up_at = models.DateTimeField(null=True, blank=True)

    status = models.IntegerField(choices=BatchStatus.choices, default=BatchStatus.NEW)
    actions_needed = models.IntegerField(
        choices=BatchNeededActions.choices,
        default=BatchNeededActions.NO_ACTION_NEEDED,
    )

    batch_type = models.IntegerField(
        choices=BatchType.choices,
        default=BatchType.NOT_SET,
    )
    cnc_files_status = models.IntegerField(
        choices=ProductionFileStatus.choices,
        default=ProductionFileStatus.INITIAL,
    )
    production_files_status = models.IntegerField(
        choices=ProductionFileStatus.choices,
        default=ProductionFileStatus.INITIAL,
    )
    packaging_files_status = models.IntegerField(
        choices=ProductionFileStatus.choices,
        default=ProductionFileStatus.INITIAL,
    )
    material_description = models.CharField(max_length=350, default='', blank=True)

    product_type = models.CharField(max_length=50)
    locked_at = models.DateTimeField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    uploaded_to_meblepl_at = models.DateTimeField(null=True, blank=True)
    is_meblepl_upload_confirmed = models.BooleanField(default=False)
    is_auto_generated = models.BooleanField(default=False)
    auto_mail_was_sent = models.BooleanField(default=False)
    withdrawn_from_production = models.DateTimeField(null=True, blank=True)

    objects = BatchManager()

    class Meta:
        verbose_name = 'Batch in production'
        verbose_name_plural = 'Batches in production'
        ordering = ('-id',)

    def __str__(self):
        if self.material_description:
            return 'B{:d} - {}, {}'.format(
                self.id, self.manufactor, self.material_description
            )
        else:
            return 'B{:d} - {}'.format(self.id, self.manufactor)

    def delete(self, using=None, keep_parents=False):
        for product in self.batch_items.all():
            product.manufactor = None
            product.status_updater.change_status(ProductStatus.NEW)
            product.save()
        super().delete(using=using, keep_parents=keep_parents)

    @property
    def actions_needed_flow(self) -> BatchActionsNeededFlow:
        return BatchActionsNeededFlow(self)

    @property
    def has_products_with_errors(self):
        return any(product.item_has_errors() for product in self.batch_items.all())

    @property
    def shelf_code(self):
        """Two letter code used by production to distinguish shelf types."""
        first_item = self.batch_items.first()
        return first_item.shelf_code

    def _create_details(self):
        product_type = self.product_type
        if product_type is None or product_type == '':
            product_type = 'jetty'
        pbd_class = import_object(
            'producers.models_split.product_batch_details.ProductBatchDetails{}'.format(
                product_type.capitalize(),
            ),
        )
        obj, _ = pbd_class.objects.get_or_create(product_batch=self)
        return obj

    @property
    def has_complaints(self):
        return self.batch_type == BatchType.COMPLAINTS

    @property
    def details(self):
        try:
            if self.product_type is None or self.product_type == '':
                product_type = 'jetty'
            else:
                product_type = self.product_type
            return getattr(self, 'batch_details_{}'.format(product_type))
        except ObjectDoesNotExist:
            # Place where BatchDetail is created (Do not remove it)
            return self._create_details()

    @property
    def details_serialized(self):
        return self.details.get_serialized()

    def sent_to_customer(self, status, user=None):
        self.status = BatchStatus.SENT_TO_CUSTOMER
        self.picked_up_at = datetime.datetime.now()
        self.save()
        # todo: lets now not set sent_to_customer here

    def undo_sent_to_customer(self, status, user=None):
        self.status = BatchStatus.IN_PRODUCTION
        self.picked_up_at = None
        self.save()

    def get_custom_number(self):
        customs_numbers = list(
            self.batch_items.filter(
                order__order_type=OrderType.CUSTOM_ORDER
            ).values_list('id', flat=True)
        )
        return len(customs_numbers)

    def get_batch_items_number(self):
        return self.batch_items.count()

    get_batch_items_number.short_description = 'Items'

    def batch_value(self):
        return sum(
            getattr(product.order_item, 'price', 0)
            for product in self.batch_items.all()
        )

    def get_batch_color(self, as_rgb=True, in_polish=False):
        item = self.batch_items.first()
        try:
            item_material = item.order_item.order_item.material
        except AttributeError:
            return '-'
        if in_polish is True:
            if item_material == 1:
                return 'czarny'
            elif item_material == 3:
                return 'szary'
            else:
                return 'biały'

        if item.cached_product_type == 'jetty' and item.order_item:
            if as_rgb is True:
                return item.order_item.order_item.get_material_description()['colour'][
                    'html'
                ]
            else:
                return item_material
        else:
            return '#de1b1b'

    def sorted_batch_items(self):
        return self.batch_items.order_by('status')

    def admin_get_reproduction_time_in_days(self):
        products = self.batch_items.filter(
            reproduction_time_in_days__isnull=False
        ).order_by('-reproduction_time_in_days')
        products = [p for p in self.batch_items.all() if p.reproduction_time_in_days]
        if products:
            products = sorted(
                products, key=lambda p: p.reproduction_time_in_days, reverse=True
            )
            return products[0].reproduction_time_in_days
        return settings.DEFAULT_REPRODUCTION_TIME_FOR_ELEMENT

    admin_get_reproduction_time_in_days.short_description = 'ReprDays'

    def get_batch_type(self):
        first_product = next(iter(self.batch_items.all()), None)
        try:
            return first_product.cached_shelf_type
        except AttributeError:
            return OTHER_BATCH_TYPE_STR

    def collect_logistic_data(self) -> dict:
        """Packaging labels need additional logistic data."""
        return {
            product.id: product.collect_product_logistic_data()
            for product in self.batch_items.all()
        }

    def get_delayed_items(self):
        delayed_production_date_limit = add_business_days(
            from_date=timezone.now(),
            business_days_to_add=(
                COMPLAINT_DELAY_DAYS_LIMIT
                if self.batch_type == BatchType.COMPLAINTS
                else PRODUCT_DELAY_DAYS_LIMIT
            ),
            reverse=True,
        )
        if self.created_at < delayed_production_date_limit:
            return list(
                self.batch_items.filter(
                    status__in=ProductStatus.not_ready(),
                ).values_list('id', flat=True)
            )
        return []

    def get_total_area_and_banding_length(self):
        total_area = 0
        total_banding_length = 0
        for item in self.batch_items.all():
            total_area += item.cached_area
            total_banding_length += (
                item.cached_banding_length or item.get_banding_length()
            )
        return total_area, total_banding_length

    def get_days_of_production_for_completed_batch(self):
        if all(
            product.status >= ProductStatus.SENT_TO_CUSTOMER
            for product in self.batch_items.all()
        ):
            to_be_shipped_dates = []
            for product in self.batch_items.all():
                if product.status >= ProductStatus.SENT_TO_CUSTOMER:
                    for status_history in product.product_status_history.filter(
                        status=ProductStatus.TO_BE_SHIPPED
                    ):
                        to_be_shipped_dates.append(status_history.changed_at)
            max_date = max(to_be_shipped_dates, default=None)
            if max_date:
                return (max_date - self.created_at).days
        return '-'

    def has_pending_quality_hold_requests(self) -> bool:
        return QualityHoldReleaseRequest.objects.pending_quality_hold_requests(
            batch=self
        ).exists()

    def get_cached_batch_description_for_manufactor(self, regenerate=False):
        batch_in_cache = cache.get('producer_batch_{}'.format(self.id))
        if (
            batch_in_cache is None
            or regenerate
            or self.actions_needed != BatchNeededActions.NO_ACTION_NEEDED
        ):
            total_area, total_banding_length = self.get_total_area_and_banding_length()
            batch_entry = {
                'batch_id': self.id,
                'batch_type': self.get_batch_type_display(),
                'is_sidebohr': all(
                    [item.is_sidebohr for item in self.batch_items.all()]
                ),
                'material_description': self.material_description,
                'created_at': self.created_at.strftime('%Y/%m/%d %H:%M'),
                'batch_status': self.get_status_display(),
                'priorities': self.get_batch_priorities(),
                'actions_needed': self.get_actions_needed_display(),
                'cnc_files_status': self.cnc_files_status,
                'production_files_status': self.production_files_status,
                'packaging_files_status': self.packaging_files_status,
                'delayed_items': self.get_delayed_items(),
                'items': self.get_batch_items(),
                'completed_products': self.get_completed_products(),
                'element_order_ids': [
                    element_order.id
                    for element_order in self.elementsorder_set.all()
                    if element_order.is_standard_type
                ],
                'area': total_area,
                'banding_length': float(total_banding_length),
                'days_of_production': self.get_days_of_production_for_completed_batch(),
                'withdrawn_from_production': self.withdrawn_from_production,
                'pending_quality_hold_requests': self.has_pending_quality_hold_requests(),  # noqa: E501
            }
            cache.set(
                'producer_batch_{}'.format(self.id),
                json.dumps(batch_entry),
                60 * 60 * 60,
            )
            return batch_entry
        else:
            batch_entry = json.loads(batch_in_cache)
            batch_entry['actions_needed'] = self.get_actions_needed_display()
            batch_entry['cnc_files_status'] = self.cnc_files_status
            batch_entry['production_files_status'] = self.production_files_status
            batch_entry['packaging_files_status'] = self.packaging_files_status
            batch_entry['element_order_ids'] = (
                [
                    element_order.id
                    for element_order in self.elementsorder_set.all()
                    if element_order.is_standard_type
                ],
            )
            batch_entry['delayed_items'] = self.get_delayed_items()
            batch_entry['priorities'] = self.get_batch_priorities()
            batch_entry['completed_products'] = self.get_completed_products()
            batch_entry[
                'days_of_production'
            ] = self.get_days_of_production_for_completed_batch()
            batch_entry['withdrawn_from_production'] = self.withdrawn_from_production
            batch_entry['items'] = self.get_batch_items()
            batch_entry[
                'pending_quality_hold_requests'
            ] = self.has_pending_quality_hold_requests()
            return batch_entry

    def get_batch_items(self):
        return sorted(
            [{'id': item.id, 'status': item.status} for item in self.batch_items.all()],
            key=itemgetter('id'),
        )

    def get_batch_priorities(self):
        batch_priorities = defaultdict(int)
        for product in self.batch_items.exclude(
            priority__in=ProductPriority.on_hold_priorities()
        ):
            merged_priorities = product.merged_priorities
            if merged_priorities:
                batch_priorities[merged_priorities] += 1
        return batch_priorities

    def get_completed_products(self):
        return {
            'all': self.batch_items.exclude(status=ProductStatus.ABORTED).count(),
            'completed': self.batch_items.filter(
                status__gte=ProductStatus.SENT_TO_CUSTOMER
            ).count(),
        }

    @property
    def is_sotty(self):
        return self.product_type == Furniture.sotty.value

    def generate_sotty_files(self):
        has_covers = False
        sofas = []

        for product in self.batch_items.all():
            if product.cover_only and not has_covers:
                has_covers = True
            else:
                sofas.append(product)

        if has_covers:
            self.details.generate_labels_elements()
            self.details.generate_labels_packaging()

        # generate these files only for sofas, skip covers
        for product in sofas:
            product.details.generate_instruction()
            product.details.generate_labels_packaging()


class ProductBatchComplaint(ProductBatch):
    objects = ProductBatchComplaintManager()

    class Meta:
        proxy = True
        verbose_name = 'Complaint batches in Production'


class Product(
    ProductionSystemClientMixin,
    LoggerMixin,
    SafeDeleteModel,
):

    JETTY = 'jetty'
    WATTY = 'watty'
    SOTTY = 'sotty'
    JETTY_WATTY = {JETTY, WATTY}
    PS_SERIALIZED = {JETTY, WATTY, SOTTY}

    order = models.ForeignKey(
        'orders.Order',
        on_delete=models.CASCADE,
    )
    logistic_order = models.IntegerField(null=True)

    order_item = models.ForeignKey(
        'orders.OrderItem',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )  # can be null for prototypes
    order_item_serialized = models.JSONField(null=True, blank=True)

    copy_of = models.ForeignKey(
        'self',
        related_name='reproduction_products',
        help_text='parent product, used in reproductions',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )

    quality_hold_release_request = models.ForeignKey(
        'QualityHoldReleaseRequest',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='products',
    )

    additional_order_items = models.ManyToManyField(
        'orders.OrderItem',
        related_name='additional_order_items',
        blank=True,
    )  # for sale order, return order and so on

    manufactor = models.ForeignKey(
        Manufactor,
        null=True,
        blank=True,
        related_name='products',
        on_delete=models.CASCADE,
    )
    warehouse = models.ForeignKey(
        Manufactor,
        null=True,
        blank=True,
        related_name='products_in_warehouse',
        on_delete=models.CASCADE,
    )
    batch = models.ForeignKey(
        ProductBatch,
        null=True,
        blank=True,
        related_name='batch_items',
        on_delete=models.SET_NULL,
    )
    gala_status = models.CharField(max_length=100, blank=True, default='')
    status = models.IntegerField(
        choices=ProductStatus.choices,
        default=ProductStatus.NEW,
        db_index=True,
    )
    previous_status = models.IntegerField(
        choices=ProductStatus.choices,
        default=ProductStatus.NEW,
    )
    cached_product_type = models.CharField(max_length=250, default='', db_index=True)
    cover_only = models.BooleanField(default=False)

    display_notes_on_front_view = models.BooleanField(default=True)
    notes = models.TextField(blank=True, default='')
    priority = models.IntegerField(
        choices=ProductPriority.choices,
        default=ProductPriority.NORMAL,
    )
    delivery_priority = models.IntegerField(
        choices=DeliveryPriority.choices,
        default=DeliveryPriority.NORMAL,
    )
    source_priority = models.IntegerField(
        choices=SourcePriority.choices,
        default=SourcePriority.NORMAL,
    )
    quality_control_needed = models.BooleanField(
        default=False,
        help_text='Manual Quality Control Needed (factory visit)?',
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    delivered_at = models.DateField(blank=True, null=True)

    added_referral = models.CharField(max_length=200, blank=True, default='')

    has_errors = models.BooleanField(
        editable=False,
        default=None,
        db_index=True,
        null=True,
    )
    cached_depth = models.IntegerField(
        default=None, null=True, editable=False, db_index=True
    )
    cached_width = models.IntegerField(
        default=None, null=True, editable=False, db_index=True
    )
    cached_height = models.IntegerField(default=None, null=True)
    cached_has_top_or_bottom_storage = models.BooleanField(
        default=False,
        editable=False,
        db_index=True,
    )
    cached_shelf_type = models.CharField(
        max_length=3, blank=True, default='', editable=False, db_index=True
    )
    # cached_dna_style - named in some places `pattern` which is misleading
    cached_dna_style = models.IntegerField(
        default=None,
        null=True,
        editable=False,
        db_index=True,
    )
    is_extended = models.BooleanField(
        editable=False,
        default=None,
        db_index=True,
        null=True,
    )
    is_sidebohr = models.BooleanField(
        editable=False,
        default=None,
        db_index=True,
        null=True,
    )
    is_desk = models.BooleanField(
        editable=False,
        default=None,
        db_index=True,
        null=True,
    )
    has_doors = models.BooleanField(
        editable=False,
        default=None,
        db_index=True,
        null=True,
    )
    has_plus_feature = models.BooleanField(
        editable=False,
        db_index=True,
        null=True,
    )
    has_plinth = models.BooleanField(
        editable=False,
        db_index=True,
        null=True,
    )
    has_drawers = models.BooleanField(
        editable=False,
        default=None,
        db_index=True,
        null=True,
    )
    cached_material = models.IntegerField(
        default=None, null=True, editable=False, db_index=True
    )
    cached_materials = models.JSONField(
        default=list, null=True, editable=False, db_index=True
    )
    cached_material_color = models.CharField(
        max_length=50, blank=True, default='', editable=False
    )
    cached_second_color = models.CharField(
        max_length=50,
        blank=True,
        default='',
        editable=False,
    )
    cached_area = models.FloatField(default=None, null=True, editable=False)
    cached_banding_length = models.DecimalField(
        max_digits=6, decimal_places=2, null=True, blank=True
    )
    cached_configurator_type = models.IntegerField(
        default=ConfiguratorTypeEnum.ROW.value,
        null=True,
        editable=False,
        choices=ConfiguratorTypeEnum.choices(),
    )
    cached_physical_product_version = models.IntegerField(
        default=PhysicalProductVersion.TREX.value,
        null=True,
        editable=False,
        choices=PhysicalProductVersion.choices(),
    )
    reproduction_time_in_days = models.PositiveSmallIntegerField(
        null=True,
        default=None,
        blank=True,
        help_text=(
            'Reproduction days that producer need to product given complaint element'
        ),
        verbose_name='ReprDays',
    )
    quality_priority = models.IntegerField(
        choices=QualityPriorityChoices.choices,
        default=None,
        null=True,
        blank=True,
    )
    quality_notes = models.TextField(blank=True, default='')
    quality_date = models.DateTimeField(null=True, blank=True)
    quality_result = models.IntegerField(
        choices=QualityResultChoices.choices,
        default=None,
        null=True,
        blank=True,
    )
    quality_report_link = models.CharField(blank=True, default='', max_length=128)

    requested_postponed_delivery_date = models.DateField(blank=True, null=True)
    expected_postponed_release_date = models.DateField(blank=True, null=True)
    estimated_production_date = models.DateField(blank=True, null=True)

    is_recovered = models.BooleanField(default=False)
    recovered_at = models.DateTimeField(null=True, blank=True)

    recovery_report = models.ForeignKey(
        'material_recovery.MaterialRecoveryReport',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
    )
    has_double_lamello = models.BooleanField(
        editable=False,
        default=None,
        db_index=True,
        null=True,
    )
    has_left_door_with_handle = models.BooleanField(
        editable=False,
        default=None,
        db_index=True,
        null=True,
    )
    has_back_top_b = models.BooleanField(
        editable=False,
        default=None,
        db_index=True,
        null=True,
    )
    has_wall_ee = models.BooleanField(
        editable=False,
        default=None,
        db_index=True,
        null=True,
    )
    has_lighting = models.BooleanField(
        editable=False,
        db_index=True,
        null=True,
    )
    has_topology = models.BooleanField(
        editable=False,
        db_index=True,
        null=True,
    )
    vertical_heights = models.TextField(blank=True, default='')

    SERIALIZATION_FIELDS = [
        'logistic_order',
    ]

    objects = ProductManager()

    class Meta:
        verbose_name = 'Product in production'
        verbose_name_plural = 'Products in production'

    def __str__(self):
        shelf_type = self.cached_shelf_type or 'T?'
        manufactor = self.manufactor if self.manufactor is not None else '-'
        return (
            f'Product {self.id}, Item: {self.order_item} {shelf_type} for {manufactor}'
        )

    @property
    def does_instruction_file_exist(self):
        return (
            self.details
            and self.details.instruction
            and self.details.instruction.storage.exists(self.details.instruction.name)
        )

    def set_priority_from_order(self):
        if self.order.is_design_change_needed:
            self.priority_updater.change_priority(ProductPriority.ON_HOLD_DES)
        if self.order.assembly or self.order_item.is_assembly():
            self.priority_updater.set_assembly_delivery_priority()
        self.priority_updater.set_source_priority_on_init()

    @property
    def shelf_code(self) -> str:
        """Two letter code used by production to distinguish shelf types."""
        return self.cached_shelf_type

    @classmethod
    def from_db(cls, db, field_names, values):
        """Store Product's batch id, to alert it in case it is changed or removed."""
        instance = super().from_db(db, field_names, values)
        instance._previous_batch_id = instance.batch_id
        return instance

    def save_serialization_only(self, *args, **kwargs):
        """
        It's dedicated save that should be called with update_fields parameter
        with SERIALIZATION_FIELDS only, to avoid side effects from our custom save
        """
        update_fields = kwargs.get('update_fields')
        assert set(update_fields).issubset(self.SERIALIZATION_FIELDS)
        return super().save(*args, **kwargs)

    def save(self, *args, **kwargs):
        previous_batch_id = getattr(self, '_previous_batch_id', None)
        if (
            not self._state.adding
            and previous_batch_id
            and previous_batch_id != self.batch_id
        ):
            try:
                previous_batch = ProductBatch.objects.get(id=previous_batch_id)
            except ProductBatch.DoesNotExist:
                pass
            else:
                previous_batch.actions_needed_flow.process_product_removed_from_batch()
            self._previous_batch_id = self.batch_id
        super().save(*args, **kwargs)

        self.status_updater.check_status_change()

    @admin.display(description='Priority')
    def get_priority_display_with_switched_on_hold(self):
        if self.priority == ProductPriority.ON_HOLD and self.order_item.deleted:
            return ProductPriority.on_hold_switch_label()
        return self.get_priority_display()

    def get_sliders(self):
        if self.cached_product_type != Product.WATTY:
            return []
        materials = (
            self.details_serialized.get('cached_serialization', {})
            .get('materials', {})
            .get('material_data', [])
        )
        result = set()
        for code in materials:
            if 'slide' in code and 'locking-mechanism' not in code:
                result.update([code.replace('fitting_slide_', '')])
        return list(result)

    @property
    def status_updater(self) -> ProductStatusUpdater:
        return ProductStatusUpdater(self)

    @property
    def serialization_updater(self) -> ProductSerializationUpdater:
        return ProductSerializationUpdater(self)

    # --- SERIALIZACJA - GENEROWANIE DANYCH -------------------------------------------
    def match_log(self):
        log = Log.objects.filter(model=self._meta.label).filter(
            Q(model_id=self.pk) | Q(model_id=None, data__contains=[self.pk])
        )
        return log

    def get_logistic_order(self):
        return self.order.get_logistic_order_by_id(self.logistic_order)

    def get_delivery_prediction(self, transport_type):
        logistic_order = self.get_logistic_order()
        if logistic_order and logistic_order.undamaged_delivery_prediction:
            return f'{getattr(logistic_order.undamaged_delivery_prediction, transport_type)}%'  # noqa E501
        return None

    # --- SERIALIZACJA ----------------------------------------------------------------

    def get_gallery_item_serialized(self, order=None):
        gallery_item = self.order_item.order_item
        furniture_serializer = furniture_serializer_class_factory(gallery_item)
        if not furniture_serializer:
            raise ValueError(f'Unknown gallery item type: {gallery_item.product_type}')
        return furniture_serializer(gallery_item, context={'order': order}).data

    def get_serialized_product_info(self):
        if not self.details.cached_serialization:
            self.save_error()
            raise SerializationMissingError(
                'Missing serialization for product %s', self.id
            )
        return self.details.cached_serialization

    def save_error(self):
        if not self.has_errors:
            self.has_errors = True
            self.save(update_fields=['has_errors'])

    def get_jetty_serialized(self):
        """This function gets cached version of serialization if it exists."""
        return JettySerialized(self.get_serialized_product_info(), self)

    # TODO: to be changed to something nicer, for now it should work.
    #  Teodor, this is the place.
    def get_serialization(self):
        return JettySerialized(self.get_serialized_product_info())

    def get_serialization_version(self):
        return self.get_jetty_serialized().serialization_version

    # --- SERIALIZACJA ----------------------------------------------------------------

    def get_production_ivy(self):
        return self.get_jetty_serialized().production_ivy

    def get_materials_usage_df(self, include_prefixes=False, pricing_factor_df=None):
        return self.get_jetty_serialized().get_materials_usage_df(
            include_prefixes=include_prefixes, pricing_factor_df=pricing_factor_df
        )

    @staticmethod
    def get_materials_usage_df_from_product_serialization(
        product_serialization, include_prefixes=False
    ):
        return JettySerialized(product_serialization).get_materials_usage_df(
            include_prefixes=include_prefixes
        )

    # ---------------------------------------------------------------------------------

    def get_cached_product_type(self):
        return self.cached_product_type

    get_cached_product_type.short_description = 'Type'

    priority.short_description = 'P'

    @property
    def reproduction_origin_complaint(self) -> 'Complaint':
        """
        Returns Complaint object that generated this product.

        Checks for complaints that have `reproduction_product` set to this product.
        """
        possible_complaint = None
        if self.copy_of is not None:
            possible_complaints_count = self.reproduction_complaints.count()
            if possible_complaints_count == 1:
                possible_complaint = self.reproduction_complaints.last()
            else:
                logger.error(
                    'Product %s has odd number of complaints: %s',
                    self.id,
                    possible_complaints_count,
                )
        return possible_complaint

    @property
    def product_type(self):
        if self.cached_product_type == '' or self.cached_product_type is None:
            if self.order_item is None:
                return 'prototype'
            self.cached_product_type = self.order_item.content_type.model
            self.save()
            return self.cached_product_type
        else:
            return self.cached_product_type

    @property
    def product_details(self):
        return getattr(self, f'product_details_{self.product_type}', None)

    def _create_details(self):
        product_type = self.product_type
        pd_class = import_object(
            'producers.models_split.product_details.ProductDetails{}'.format(
                product_type.capitalize()
            ),
        )
        obj, _ = pd_class.objects.get_or_create(product=self)
        return obj

    def get_product_id(self):
        if self.cached_shelf_type:
            value = f'{self.cached_shelf_type}_{self.id}'
        else:
            value = f'?_{self.id}'

        if self.order.order_type == OrderType.COMPLAINT:
            if (
                self.order.complaints.exists()
                and self.order.is_to_be_shipped_complaint_express_replacement()
            ):
                return f'{value}_express'
            return f'{value}_reklamacja'
        return value

    @property
    def details(self):
        try:
            if self.product_type == Furniture.jetty.value:
                return self.product_details_jetty
            elif self.product_type == Furniture.watty.value:
                return self.product_details_watty
            elif self.product_type == Furniture.sotty.value:
                return self.product_details_sotty
            return getattr(self, 'product_details_{}'.format(self.product_type))
        except ObjectDoesNotExist:
            return self._create_details()

    def get_product_file_link(self, file_attr):
        product_details = self.product_details
        if not product_details:
            return ''
        file = getattr(product_details, file_attr, None)
        return f'<a href="{file.url}">{self.pk}</a>' if file else ''

    @property
    def details_serialized(self):
        return self.details.get_serialized()

    @cache_model_method(cache_period=86400)
    def get_placed_date(self):
        if self.order.placed_at is not None:
            return self.order.placed_at
        if self.order.paid_at is not None:
            return self.order.paid_at
        try:
            return (
                self.product_status_history.filter(
                    status__in=[
                        ProductStatus.ASSIGNED_TO_PRODUCTION,
                        ProductStatus.NEW,
                        ProductStatus.IN_PRODUCTION,
                    ]
                )
                .first()
                .changed_at
            )
        except AttributeError:
            candidate_dates = [
                _f for _f in [self.order.paid_at, self.order.settled_at] if _f
            ]
            if len(candidate_dates) > 0:
                return sorted(candidate_dates)[0]
            else:
                return self.created_at

    @cache_model_method(cache_period=86400)
    def get_in_production_date(self):
        try:
            return (
                self.product_status_history.filter(
                    status__in=[
                        ProductStatus.ASSIGNED_TO_PRODUCTION,
                        ProductStatus.NEW,
                        ProductStatus.IN_PRODUCTION,
                    ]
                )
                .last()
                .changed_at
            )
        except AttributeError:
            return None

    @cache_model_method(cache_period=86400)
    def get_finished_production_date(self):
        try:
            return (
                self.product_status_history.filter(
                    status__in=[
                        ProductStatus.SENT_TO_WAREHOUSE,
                        ProductStatus.SENT_TO_CUSTOMER,
                        ProductStatus.INTERNAL_USAGE,
                        ProductStatus.DELIVERED_TO_CUSTOMER,
                        ProductStatus.TO_BE_SHIPPED,
                        ProductStatus.ABORTED_DONE,
                    ]
                )
                .first()
                .changed_at
            )
        except AttributeError:
            return None

    @cache_model_method(cache_period=86400)
    def get_sent_to_customer_date(self):
        try:
            return (
                self.product_status_history.filter(
                    status__in=[
                        ProductStatus.SENT_TO_CUSTOMER,
                        ProductStatus.DELIVERED_TO_CUSTOMER,
                    ]
                )
                .first()
                .changed_at
            )
        except AttributeError:
            logistic_order = self.get_logistic_order()
            if logistic_order is not None and logistic_order.sent_to_customer:
                return logistic_order.sent_to_customer
            if self.delivered_at is not None:
                return self.delivered_at - datetime.timedelta(days=2)
            return None

    def get_delivered_to_customer_date(self):
        sent_to_customer = self.get_sent_to_customer_date()
        if isinstance(sent_to_customer, datetime.datetime):
            sent_to_customer = sent_to_customer.date()
        if self.delivered_at is None:
            return sent_to_customer
        if sent_to_customer is None:
            return self.delivered_at
        if sent_to_customer <= self.delivered_at:
            return self.delivered_at
        else:
            return sent_to_customer + datetime.timedelta(days=2)

    def item_is_extended(self):
        if self.cached_product_type != 'jetty':
            return False  # only Jetties have joints
        return self.order_item.order_item.extended

    def item_is_sidebohr(self):
        """
        We mark Sideboard as `Sidebohr` if it has any side tylkliks in horizontals.
        """
        serialization = self.details.cached_serialization
        if not serialization or not serialization.get('item'):
            return False
        if self.cached_physical_product_version == PhysicalProductVersion.TREX:
            # old shelves don't have tylkliks
            return False

        for element in self.get_cached_serialization_elements():
            if element['elem_type'] == 'H' and any(
                fitting.get('cnc_info') == 'tylklik'
                and fitting.get('connected_to') in {3, 4}
                for fitting in element.get('fittings', [])
            ):
                return True
        return False

    def get_item_configurator_type(self):
        return ConfiguratorTypeEnum(self.order_item.order_item.configurator_type)

    def get_item_physical_product_version(self):
        return PhysicalProductVersion(
            self.order_item.order_item.physical_product_version
        )

    def item_has_errors(self):
        serialization = self.details.cached_serialization
        if not serialization:
            return True
        return len(serialization.get('errors', [])) > 0

    def get_shelf_type(self, with_html=True, with_configurator_type=False):
        if self.cached_shelf_type:
            try:
                shelf_type_and_row_type = '{}{}, {} rows'.format(
                    (
                        ''
                        if self.copy_of is None
                        else 'REPRODUCTION for {}, '.format(self.copy_of_id)
                    ),
                    self.cached_shelf_type,
                    self.get_rows_type(),
                )
                if with_configurator_type:
                    shelf_type_and_row_type = format_html(
                        '''
                        {shelf_type}<br/>
                        {configurator}<br/>
                        {physical_version}
                        ''',
                        shelf_type=shelf_type_and_row_type,
                        configurator=ConfiguratorTypeEnum(
                            self.cached_configurator_type,
                        ).name,
                        physical_version=PhysicalProductVersion(
                            self.cached_physical_product_version,
                        ).name,
                    )
                return shelf_type_and_row_type

            except (
                KeyError,
                AttributeError,
            ):  # we have no rows in serialization pass to old method
                pass
        if self.product_type == 'jetty':
            shelf_type, row_type = None, 'UNKNOWN'
            if (
                hasattr(self, 'order_item')
                and hasattr(self.order_item, 'order_item')
                and hasattr(self.order_item.order_item, 'shelf_type')
            ):
                shelf_type = self.order_item.order_item.shelf_type
            if shelf_type is not None:
                rows = set(self.order_item.order_item.rows)
                old, new = {208, 278, 398}, {200, 300, 400, 500, 600, 700, 800}
                row_type = (
                    'NEW'
                    if len(rows - new) == 0
                    else 'OLD'
                    if len(rows - old) == 0
                    else 'CUSTOM'
                )
            if with_html:
                return '{}<br/><b>{}</b> rows'.format(
                    {0: 'Type 1', 1: 'Type 2'}.get(shelf_type, 'other'), row_type
                )
            else:
                return '{}, {} rows'.format(
                    {0: 'Type 1', 1: 'Type 2'}.get(shelf_type, 'other'), row_type
                )
        if self.product_type == 'watty':
            shelf_type, row_type = None, 'UNKNOWN'
            if (
                hasattr(self, 'order_item')
                and hasattr(self.order_item, 'order_item')
                and hasattr(self.order_item.order_item, 'shelf_type')
            ):
                shelf_type = self.order_item.order_item.shelf_type
            if with_html:
                return '{}<br/><b>{}</b> rows'.format(
                    {0: 'Type 1', 1: 'Type 2'}.get(shelf_type, 'other'), row_type
                )
            else:
                return '{}, {} rows'.format(
                    {0: 'Type 1', 1: 'Type 2'}.get(shelf_type, 'other'), row_type
                )
        else:
            return ''

    def get_rows_type(self):
        return {0: 'OLD', 1: 'NEW'}.get(
            self.details.cached_serialization.get('item').get('rows_type'),
            'CUSTOM',
        )

    @staticmethod
    def _blank_codename_area_multiplier(codename):
        """Multiplier to calculate board area [m2] from blank semiproduct usage [m]."""
        blank_height_m = {
            'A': 0.175,
            'B': 0.275,
            'C': 0.375,
            'D': 0.475,
            'E': 0.575,
            'F': 0.675,
            'G': 0.775,
        }
        try:
            # blank codenames pattern: `semiproduct_blank_{color}_{height}{producer}`
            height_name = codename.split('_')[3][0]
        except IndexError:
            return 0
        else:
            return blank_height_m.get(height_name, 0)

    def _get_area_from_usage(self) -> float:
        """More accurate board area [m2] calculated from material usage."""
        try:
            materials = self.details.cached_serialization['materials']['material_data']
            semiproducts = self.details.cached_serialization['materials'][
                'semiproducts_data'
            ]
        except KeyError:
            return 0
        board_materials_data = (
            material_data.get('usage', 0)
            for codename, material_data in materials.items()
            if (
                codename.startswith('material_chipboard')
                or codename.startswith('material_plywood')
                or codename.startswith('material_MDF')
            )
        )
        blank_materials_data = (
            (
                material_data.get('usage', 0)
                * self._blank_codename_area_multiplier(codename)
            )
            for codename, material_data in semiproducts.items()
            if (
                codename.startswith('semiproduct_blank_ash')
                or codename.startswith('semiproduct_blank_oak')
            )
        )
        board_usage_sum = sum(board_materials_data) + sum(blank_materials_data)
        return round(board_usage_sum, 2)

    def get_m2(self):
        """Get board area [m2]."""
        if not self.details.cached_serialization:
            self.details.cached_serialization = (
                self.serialization_updater.gen_product_serialization()
            )
        return self._get_area_from_usage()

    def _get_banding_length_from_serialization(self):
        try:
            materials = self.details.cached_serialization['materials']['material_data']
        except KeyError:
            return 0
        banding_length_data = (
            material_data.get('usage', 0)
            for codename, material_data in materials.items()
            if codename.startswith('material_ABS')
            or codename.startswith('material_band')
        )
        banding_length_sum = sum(banding_length_data)
        return Decimal(banding_length_sum).quantize(Decimal('.01'))

    def get_banding_length(self):
        """Get banding length [m]."""
        if not self.details.cached_serialization:
            self.details.cached_serialization = (
                self.serialization_updater.gen_product_serialization()
            )
        return self._get_banding_length_from_serialization()

    def get_note(self):
        if self.cached_product_type == 'jetty' and self.order_item:
            item = self.order_item.order_item
            info_string = []
            if len(item.joints) > 0 or item.width > 2400 or item.get_width() > 240:
                info_string.append('EXTENDED')
            if len(item.drawers) > 0:
                info_string.append('DRAWERS')
            if len(item.doors) > 0:
                info_string.append('DOORS')
            return ' + '.join(info_string)
        return ''

    @property
    def priority_updater(self) -> ProductPriorityUpdater:
        return ProductPriorityUpdater(self)

    def get_order_customer_as_string(self):
        return escape(self.order.get_customer_as_string())

    get_order_customer_as_string.short_description = 'Customer'

    def get_order_country_as_string(self):
        return self.order.country

    get_order_country_as_string.short_description = 'Country'

    def get_order_id(self):
        order_type = (self.order.order_type if self.order else '',)
        return safe(
            f"<a href='/admin/orders/order/{self.order_id}/'>{self.order_id}</a>"
            f"<span class='order_type' style='display: none'>{order_type}</span>"
        )

    def get_packaging(self):
        if self.cached_product_type not in Product.PS_SERIALIZED:
            return None

        try:
            return self.get_jetty_serialized().packaging
        except IntegrityError:
            logger.critical('ProductDetails already exists %s', self.id, exc_info=True)
        except Exception:
            logger.critical('PAKOWANIE UMARLO DLA ITEMU %s', self.id, exc_info=True)
        return None

    def get_packaging_quantity(self):
        packaging = self.get_packaging()
        return len(packaging) if packaging else 0

    @property
    def released_packages(self):
        if (
            self.was_picked_up()
            or self.is_waiting_be_picked_up()
            or self.is_waiting_for_quality_control()
        ):
            return self.get_packaging_quantity()
        return self.manufacturerreleasedpackage_set.count()

    def get_elements_quantity(self):
        item = self.get_serialized_product_info().get('item', {})
        quantity = 0
        if self.is_complaint():
            for element in item.get('elements', []):
                if element.get('complaint', False):
                    quantity += 1 * self.get_element_quantity_factor(
                        element['elem_type']
                    )
        else:
            for element_type, amount in item.get('elements_amount', {}).items():
                quantity += amount * self.get_element_quantity_factor(element_type)
        return quantity

    @classmethod
    def get_element_quantity_factor(cls, element_type):
        if cls.should_element_bo_counted(element_type):
            return 0
        if cls.is_element_drawer(element_type):
            return 5
        return 1

    @classmethod
    def should_element_bo_counted(cls, element_type):
        not_counted_elements = {'J', 'L', 'Sh', 'Ll', 'G', 'Gh', 'Wf', 'Rd', 'N'}
        return element_type in not_counted_elements

    @classmethod
    def is_element_drawer(cls, element_type):
        drawers_types = {'T', 'Tb', 'Td', 'Te', 'Ti'}
        return element_type in drawers_types

    def get_weight_netto(self):
        """Get product weight without packaging."""
        if self.has_errors:
            return 0
        if self.cached_product_type in Product.PS_SERIALIZED:
            try:
                serialzation = self.get_jetty_serialized().serialization
            except SerializationMissingError:
                logger.exception('Missing serialization')
                return 0
            try:
                return serialzation['item']['weight']
            except KeyError:
                return 0
        return self.order_item.order_item.get_weight()

    def get_weight_brutto(self):
        """Get product weight with packaging."""
        if self.cached_product_type in Product.PS_SERIALIZED:
            packaging = self.get_packaging()
            if packaging is None:
                return 0
            weight_sum = 0
            for x in packaging:
                try:
                    weight_sum += x.adjusted_weight
                except AttributeError:
                    return 0
            return weight_sum
        else:
            if self.order_item.order_item:
                return self.order_item.order_item.get_accurate_weight_gross()
            return 0

    def is_complaint(self):
        return self.order.order_type == OrderType.COMPLAINT

    def is_waiting_be_picked_up(self):
        return self.status in {
            ProductStatus.TO_BE_SHIPPED,
            ProductStatus.TO_BE_SHIPPED_AWIZATION,
            ProductStatus.ABORTED_DONE,
        }

    def is_waiting_for_quality_control(self):
        return self.status == ProductStatus.QUALITY_CONTROL

    def was_picked_up(self):
        return self.status in {
            ProductStatus.SENT_TO_CUSTOMER,
            ProductStatus.SENT_TO_WAREHOUSE,
            ProductStatus.QUALITY_BLOCKER,
            ProductStatus.INTERNAL_USAGE,
            ProductStatus.SHELFMARKET,
            ProductStatus.SHELFMARKET_DONE,
            ProductStatus.DELIVERED_TO_CUSTOMER,
            ProductStatus.CM_UTILIZATION_DONE,
            ProductStatus.INTERNAL_SHIPMENT,
        }

    def owner_order_list(self):
        from orders.models import PaidOrders

        if self.order.order_type == OrderType.CUSTOM_ORDER:
            orders = PaidOrders.objects.filter(
                first_name=self.order.first_name, last_name=self.order.last_name
            )
        else:
            owner_query = Q(owner__id=self.order.owner.id)
            if self.order.email is not None and len(self.order.email) > 0:
                owner_query |= Q(email=self.order.email)
            orders = PaidOrders.objects.filter(owner_query).filter(
                (
                    Q(
                        status__in=[
                            OrderStatus.IN_PRODUCTION,
                            OrderStatus.SHIPPED,
                            OrderStatus.DELIVERED,
                        ]
                    )
                    & Q(order_type=OrderType.CUSTOMER)
                )
                | (
                    Q(
                        status__in=[
                            OrderStatus.PAYMENT_PENDING,
                            OrderStatus.IN_PRODUCTION,
                            OrderStatus.SHIPPED,
                            OrderStatus.DELIVERED,
                        ]
                    )
                    & Q(order_type__in=[OrderType.CUSTOM_ORDER, OrderType.B2B])
                )
            )
        return orders

    @cache_model_method(cache_period=30 * 60)
    def owner_order_count(self):
        return self.owner_order_list().count()

    owner_order_count.short_description = 'Repeating'

    @property
    def production_time(self):
        paid_at = self.order.paid_at
        finished_at = self.get_finished_production_date()
        if not (finished_at and paid_at):
            return None
        return finished_at - paid_at

    def get_absolute_url(self):
        return '/admin/producers/product/{}/'.format(self.id)

    def get_packaging_structure_for_export(self):
        ivy_production = self.get_production_ivy()
        ivy_packaging = ivy_production.get_packs()
        logistic_info = self.get_logistic_order()
        elements_to_return = []
        for pack in ivy_packaging:
            package_elements = []
            for elem_in_package in pack.all_elements:
                width = (
                    elem_in_package.package_info['x_domain'][1]
                    - elem_in_package.package_info['x_domain'][0]
                )
                height = (
                    elem_in_package.package_info['y_domain'][1]
                    - elem_in_package.package_info['y_domain'][0]
                )
                depth = (
                    elem_in_package.package_info['z_domain'][1]
                    - elem_in_package.package_info['z_domain'][0]
                )
                package_elements.append(
                    {
                        'product_id': self.id,
                        'pack_id': pack.pack_id,
                        'pack_type': pack.typ,
                        'elem_type': elem_in_package.ELEM_TYPE,
                        'elem_name': elem_in_package.name,
                        'elem_surname': elem_in_package.surname,
                        'width': width,
                        'height': height,
                        'depth': depth,
                        'pack_level': pack.no_levels,
                        'bottom': elem_in_package.package_info['pack_level']
                        == (pack.no_levels - 1),
                        'top': elem_in_package.package_info['pack_level'] == 0,
                    }
                )
            elements_to_return.append(
                {
                    'product_id': self.id,
                    'order_id': self.order.id,
                    'item_id': self.order_item.id,
                    'paid_at': (
                        self.order.paid_at.isoformat() if self.order.paid_at else None
                    ),
                    'sent_to_customer': (
                        logistic_info.sent_to_customer.isoformat()
                        if logistic_info.sent_to_customer
                        else None
                    ),
                    'delivered_to_customer': (
                        logistic_info.delivered_date.isoformat()
                        if logistic_info.delivered_date
                        else None
                    ),
                    'pack_id': pack.pack_id,
                    'pack_type': pack.typ,
                    'width': int(pack.dim_x),
                    'height': int(pack.dim_y),
                    'depth': int(pack.dim_z),
                    'weight': pack.weight,
                    'pack_level': pack.no_levels,
                    'elements': json.dumps(package_elements),
                }
            )
        return elements_to_return

    def get_elements_package_description(self, elements):
        ivy_production = self.get_production_ivy()
        ivy_packaging = ivy_production.get_packs(product=self)
        elements_to_return = []
        for elem in elements:
            possible_packages = []
            element_type = None
            width = None
            height = None
            depth = None
            for pack in ivy_packaging:
                for elem_in_package in pack.all_elements:
                    feature_element_name = elem[:-1] if elem.startswith('T') else elem
                    if elem_in_package.surname in [elem, feature_element_name]:
                        element_type = elem_in_package.ELEM_TYPE
                        width = (
                            elem_in_package.package_info['x_domain'][1]
                            - elem_in_package.package_info['x_domain'][0]
                        )
                        height = (
                            elem_in_package.package_info['y_domain'][1]
                            - elem_in_package.package_info['y_domain'][0]
                        )
                        depth = (
                            elem_in_package.package_info['z_domain'][1]
                            - elem_in_package.package_info['z_domain'][0]
                        )
                        possible_packages.append(
                            {
                                'product_id': self.id,
                                'pack_id': pack.pack_id,
                                'pack_type': pack.typ,
                                'width': pack.dim_x,
                                'height': pack.dim_y,
                                'depth': pack.dim_z,
                                'weight': pack.weight,
                                'element_level': elem_in_package.package_info[
                                    'pack_level'
                                ],
                                'pack_level': pack.no_levels,
                                'bottom': elem_in_package.package_info['pack_level']
                                == (pack.no_levels - 1),
                                'top': elem_in_package.package_info['pack_level'] == 0,
                            }
                        )
            elements_to_return.append(
                {
                    'element_name': elem,
                    'element_type': element_type,
                    'width': width,
                    'height': height,
                    'depth': depth,
                    'possible_packages': possible_packages,
                    'only_in_one_package': len(
                        list({x['pack_id'] for x in possible_packages})
                    )
                    == 1,
                }
            )
        return elements_to_return

    def get_elements_csv_meble_pl_filename(self) -> str:
        complaint_tag = 'r' if self.reproduction_complaints.exists() else ''
        return '{id}{complaint_tag}_elements.csv'.format(
            id=self.id,
            complaint_tag=complaint_tag,
        )

    def _check_serialization_errors_for_jetty_or_watty(self):
        return (
            self.cached_product_type not in self.JETTY_WATTY
            or self.details.cached_serialization is None
        )

    # TODO: all function below have a lot of common code,
    #  to be refactored and better exception handling here needed
    def get_weight_from_serialization(self):
        if self._check_serialization_errors_for_jetty_or_watty():
            return None

        try:
            return self.details.cached_serialization['item']['weight']
        except (AttributeError, KeyError):
            return None

    def get_features_from_serialization(self):
        if self._check_serialization_errors_for_jetty_or_watty():
            return None

        try:
            return json.dumps(self.details.cached_serialization['features_categorical'])
        except Exception:
            return None

    # will export cogs saved in product serialization
    def get_full_cogs_from_serialization(self):
        if self._check_serialization_errors_for_jetty_or_watty():
            return None
        try:
            #  item is more about item domains, not needed for cogs export
            return json.dumps(
                {
                    key: value
                    for key, value in self.details.cached_serialization.items()
                    if key != 'item'
                }
            )
        except TypeError:
            return None

    def get_cached_plywood_thickness(self):
        ppv = PhysicalProductVersion(self.cached_physical_product_version)
        return ppv.get_plywood_thickness()

    def get_express_replacement_elements(self):
        product_elements = self.get_cached_serialization_elements()
        physical_product_version = self.order_item_serialized[
            'physical_product_version'
        ]
        verticals_and_supports_and_legs = ['V', 'S', 'Ll']
        invalid_production_name_characters = ['$', '+']
        invalid_production_name_ending_characters = ['S', '2n', '2h', '2hn']
        is_t01_lte_bronto = (
            self.order_item.order_item.shelf_type == ShelfType.TYPE01.value
            and physical_product_version <= PhysicalProductVersion.BRONTO.value
        )
        is_veneer_s93 = (
            self.manufactor
            and self.manufactor.name == Manufactor.S93
            and self.shelf_type_option == ShelfType.VENEER_TYPE01
        )
        if is_t01_lte_bronto or is_veneer_s93:
            return []

        return [
            element['surname']
            for element in product_elements
            if (
                element['elem_type'] in verticals_and_supports_and_legs
                and not any(
                    [
                        production_name_char in element.get('production_name', '')
                        for production_name_char in invalid_production_name_characters
                    ]
                )
                and not any(
                    [
                        element.get('production_name', '').endswith(ending_char)
                        for ending_char in invalid_production_name_ending_characters
                    ]
                )
            )
        ]

    def get_fixed_reproduction_days_for_production_order(self):
        s93_reproduction_days = 10
        other_manufacturers_reproduction_days = 12

        if not self.manufactor:
            return '-'
        if self.manufactor.name == Manufactor.S93:
            return s93_reproduction_days
        return other_manufacturers_reproduction_days

    def get_reproduction_date_for_production_order(self):
        if self.batch:
            return add_business_days(
                self.batch.created_at,
                self.get_fixed_reproduction_days_for_production_order(),
                check_holidays=True,
            )
        return '-'

    def get_reproduction_date(self):
        if self.batch and self.reproduction_time_in_days:
            return add_business_days(
                self.batch.created_at,
                self.reproduction_time_in_days,
                check_holidays=True,
            )

    @property
    def get_updated_surnames_for_reproduction(self):
        """
        Get proper reproduction surnames (matching serialization) for a complaint.

        Sometimes surnames change between original and reproduction serialization.
        To avoid selecting wrong elements in front view, we match elements using
        names (which, we assume, are less prone to unexpected changes).
        """
        serialization = self.details.cached_serialization
        complaint = self.reproduction_origin_complaint
        if not serialization:
            return complaint.elements_for_reproduction
        cached_serialization_elements = self.get_cached_serialization_elements()
        elements_to_reproduce = complaint.element_for_reproduction_with_full_names()[0]
        surnames_to_reproduce = []
        for name, surnames in elements_to_reproduce.items():
            if name[0] == 'T':
                surnames_to_reproduce.extend(surnames)
            else:
                matching_element_surname = next(
                    (
                        element['surname']
                        for element in cached_serialization_elements
                        if element['name'] == name
                    ),
                    'missing_element',
                )
                surnames_to_reproduce.append(matching_element_surname)
        return surnames_to_reproduce

    def get_elements_and_pack_ids(self):
        serialization = self.get_serialized_product_info()
        elements = serialization.get('item', {}).get('elements')
        grouped_elements = itertools.groupby(
            elements,
            key=lambda x: (x.get('package_info') or {}).get('pack_id'),
        )
        return grouped_elements

    def get_front_view_svg_from_ps(self):
        with self._get_ps_client_context(suppress_errors=False) as client:
            svg = client.get_product_test_files(self, 'front-view-svg')

        if not svg:
            raise ValueError('PS returned empty data instead of front_view svg data')

        return svg.decode()

    def get_front_view_dxf_from_ps(self):
        with self._get_ps_client_context(suppress_errors=False) as client:
            dxf = client.get_product_test_files(self, 'front-view-dxf')

        if not dxf:
            raise ValueError('PS returned empty data instead of front_view dxf data')
        return dxf

    def get_carrier_name(self) -> str:
        logistic_order = self.get_logistic_order()
        if logistic_order and logistic_order.carrier:
            return logistic_order.carrier
        return ''

    def get_tracking_number(self) -> str:
        logistic_order = self.get_logistic_order()
        if logistic_order and logistic_order.tracking_number:
            return logistic_order.tracking_number
        return ''

    def get_delivered_date(self) -> Union[datetime.date, str]:
        logistic_order = self.get_logistic_order()
        if logistic_order and logistic_order.delivered_date:
            return logistic_order.delivered_date
        return ''

    @property
    def color_with_shelf_name(self):
        order_item = self.order_item.order_item
        return ShelfType(order_item.shelf_type).colors(order_item.material).color_name

    @property
    def color_option(self):
        order_item = self.order_item.order_item
        return self.shelf_type_option.colors(order_item.material)

    @property
    def color_en_name(self):
        with translation.override(LanguageEnum.EN):
            return str(self.color_option.translated_color)

    @property
    def shelf_type_option(self):
        order_item = self.order_item.order_item
        return ShelfType(order_item.shelf_type)

    def get_has_double_lamello(self):
        for element in self.get_cached_serialization_elements():
            if element['elem_type'] == 'H':
                cncs = element['cnc']
                male_lamellos = 0
                female_lamellos = 0
                for cnc in cncs:
                    if cnc['name'] == 'lamello_male_connector_front':
                        male_lamellos += 1
                    if cnc['name'] == 'lamello_female_connector_front':
                        female_lamellos += 1
                if male_lamellos + female_lamellos >= 2:
                    return True
        return False

    def get_has_left_door_with_handle(self):
        for element in self.get_cached_serialization_elements():
            if element['elem_type'] == 'D' and element['production_name'] == 'l':
                if element['flip'] == 1:
                    return True
        return False

    def get_has_back_top_b(self):
        for element in self.get_cached_serialization_elements():
            if element['production_name'] == 'tb':
                return True
        return False

    def get_has_wall_ee(self):
        for element in self.get_cached_serialization_elements():
            if element['production_name'] == 'ee':
                return True
        return False

    def get_vertical_heights(self) -> str:
        serialization = self.details.cached_serialization
        if not serialization or 'elements' not in serialization.get('item', {}):
            return ''
        heights = {
            element['height_name']
            for element in serialization['item']['elements']
            if element['elem_type'] == 'V'
        }
        return ''.join(heights)

    def get_country_instance(self):
        country = self.order.get_order_country()
        if not country:
            try:
                return Countries.get_country_by_name(self.order.country.lower())
            except AttributeError:
                return None
        return country

    def get_country_name_for_packaging_instruction(self):
        country = self.get_country_instance()
        if country and country.code == 'FR':
            # For now, we want to mark only France
            return Countries.countries_pl.get(country.code, '')
        return ''

    def get_cached_serialization_elements(self) -> list:
        serialization = self.details.cached_serialization
        if not serialization or 'item' not in serialization:
            return []
        return serialization['item'].get('elements', [])

    def get_last_tbs_changed_at(self):
        tbs_status_obj = self.product_status_history.filter(
            status=ProductStatus.TO_BE_SHIPPED,
        ).order_by('changed_at')

        if tbs_status_obj.exists():
            return tbs_status_obj.last().changed_at.date()
        return None

    def get_estimated_production_date_by_manufactor(self):
        from dynamic_delivery.services.ship_in_range import (
            ManufactorCapacityRangeStrategy,
        )

        if self.batch and self.batch.manufactor:
            production_range = ManufactorCapacityRangeStrategy(
                self.batch.manufactor
            ).ship_in_range()
            production_days = production_range.max_days
            return datetime.date.today() + datetime.timedelta(days=production_days)

        return None

    @property
    def merged_priorities(self):
        priorities = []
        if self.source_priority != SourcePriority.NORMAL:
            priorities.append(self.get_source_priority_display())
        if self.priority != ProductPriority.NORMAL:
            priorities.append(self.get_priority_display())
        return '_'.join(priorities)

    @property
    def is_sotty(self):
        return self.product_type == Furniture.sotty.value

    def collect_product_logistic_data(self):
        logistic_order = self.order_item.get_logistic_order()
        country = self.get_country_instance()
        return {
            'has_dedicated_transport': self.is_dt_best_transport(logistic_order),
            'name': self.order.client_or_company_name,
            'address': self.order.full_address,
            'country': country.name if country else '',
        }

    def is_dt_best_transport(self, logistic_order):
        from orders.internal_api.clients import LogisticOrderAPIClient

        DEDICATED_TRANSPORT_SHORTNAME = 'dt'

        has_dedicated_transport = False
        if self.cached_product_type == Product.JETTY and logistic_order is not None:
            logistic_order_api = LogisticOrderAPIClient()
            selected_transport = logistic_order_api.select_best_transport(
                logistic_order.id
            )
            has_dedicated_transport = (
                selected_transport == DEDICATED_TRANSPORT_SHORTNAME
            )
        return has_dedicated_transport


class ProductComplaint(Product):
    objects = ProductComplaintManager()

    class Meta:
        verbose_name = 'Complaints in Production'
        proxy = True


class ProductAbortedManager(models.Manager):
    def get_queryset(self):
        return (
            super(ProductAbortedManager, self)
            .get_queryset()
            .filter(status=ProductStatus.ABORTED)
        )


class ProductAborted(Product):
    objects = ProductAbortedManager()

    class Meta(object):
        proxy = True


class OnlyItemsInProductionManager(models.Manager):
    def get_queryset(self):
        return (
            super(OnlyItemsInProductionManager, self)
            .get_queryset()
            .filter(cached_product_type=self.model_name)
        )


class JettyInProductionManager(OnlyItemsInProductionManager):
    model_name = 'jetty'


class WattyInProductionManager(OnlyItemsInProductionManager):
    model_name = 'watty'


class ProductStatusHistory(models.Model):
    product = models.ForeignKey(
        Product,
        related_name='product_status_history',
        on_delete=models.CASCADE,
    )
    status = models.IntegerField(choices=ProductStatus.choices)
    previous_status = models.IntegerField(choices=ProductStatus.choices)
    changed_at = models.DateTimeField()
    owner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
    )
    description = models.TextField(blank=True, default='')

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta(object):
        verbose_name = 'Product status history'
        verbose_name_plural = 'Products status history'
        ordering = ('changed_at',)

    def __str__(self):
        return 'Status change for {} from {} to {} - {}'.format(
            self.product_id,
            self.get_previous_status_display(),
            self.get_status_display(),
            self.changed_at,
        )

    def get_product_id(self):
        return self.product_id

    def get_changed_at_formatted(self):
        return self.changed_at.strftime('%d/%m/%Y %H:%M:%S')


class ProductPriorityHistory(models.Model):
    product = models.ForeignKey(
        Product,
        related_name='product_priority_history',
        on_delete=models.CASCADE,
    )

    priority = models.IntegerField(choices=ProductPriority.choices)
    previous_priority = models.IntegerField(choices=ProductPriority.choices)

    created_at = models.DateTimeField(default=timezone.now)
    owner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
    )
    objects = ProductPriorityHistoryQuerySet.as_manager()

    class Meta:
        verbose_name = 'Product priority history'
        verbose_name_plural = 'Product priority history'
        ordering = ('-created_at',)

    def __str__(self):
        return (
            f'Priority change for {self.product_id} from '
            f'{self.get_previous_priority_display()} to '
            f'{self.get_priority_display()} - {self.created_at}'
        )


class ManufactorAdditionalAccounts(models.Model):
    manufactor = models.ForeignKey(
        Manufactor,
        related_name='accounts',
        on_delete=models.CASCADE,
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
    )
    access_level = models.CharField(
        max_length=100,
        choices=AccessLevel.choices,
    )

    class Meta:
        verbose_name = 'Producer additional account'
        verbose_name_plural = 'Producer additional accounts'

    def __str__(self):
        return str(self.user.username)


# TODO generic model for comunication with manufacturers API
class ManufacturerReleasedPackage(models.Model):
    product = models.ForeignKey(
        'producers.Product', on_delete=models.CASCADE, null=True
    )
    package = models.CharField(max_length=128)
    released_at = models.DateTimeField()
    manufacturer = models.ForeignKey(
        'producers.Manufactor', on_delete=models.PROTECT, null=True
    )

    MANUFACTURERS_API_INTEGRATED = [Manufactor.INEX, Manufactor.DREWTUR]


class GalaProductStatusHistory(models.Model):
    new_status = models.CharField(max_length=100)
    previous_status = models.CharField(max_length=100, blank=True, default='')
    updated_at = models.DateTimeField(auto_now=True)
    product = models.ForeignKey(
        'Product',
        on_delete=models.CASCADE,
        related_name='gala_status_history',
    )


class BatchingSettings(models.Model):
    manufactor = models.ForeignKey('producers.Manufactor', on_delete=models.CASCADE)
    shelf_type = models.PositiveSmallIntegerField(choices=ShelfType.choices())
    separate_desks = models.BooleanField(
        default=False, help_text='Separate desks from other products'
    )
    separate_s_plus = models.BooleanField(
        default=False, help_text='Separate furniture with s+ from other products'
    )
    min_size = models.PositiveIntegerField(
        default=0, help_text='Minimum number of products in a batch'
    )
    max_size = models.PositiveIntegerField(
        default=25, help_text='Maximum number of products in a batch'
    )
    max_area = models.PositiveIntegerField(default=10000000)

    objects = BatchingSettingsManager()

    class Meta:
        unique_together = ('manufactor', 'shelf_type')
        verbose_name = 'Batching settings'
        verbose_name_plural = 'Batching settings'

    def __str__(self):
        if self.id:
            name = (
                f'{self.id}: {self.manufactor}, '
                f'{self.get_shelf_type_display()}, '
                f'{self.min_size} to {self.max_size} products, '
                f'{self.max_area}m3 area'
            )
        else:
            name = (
                f'Default: from {self.min_size} to {self.max_size} products, '
                f'{self.max_area}m3 area'
            )
        if self.separate_desks:
            name += ', separate desks'
        if self.separate_s_plus:
            name += ', separate s+ furniture'
        return name


class QualityHoldReleaseRequest(models.Model):
    batch = models.ForeignKey(
        ProductBatch,
        on_delete=models.CASCADE,
        related_name='quality_hold_release_requests',
    )
    status = models.IntegerField(
        choices=ProductStatus.choices,
        help_text=(
            'The status (QC/QB) that products were in when the request was created'
        ),
    )
    created_at = models.DateTimeField(auto_now_add=True)
    accepted_at = models.DateTimeField(null=True, blank=True)
    accepted_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='accepted_quality_hold_release_requests',
    )
    rejected_at = models.DateTimeField(null=True, blank=True)
    rejected_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='rejected_quality_hold_release_requests',
    )

    objects: QualityHoldReleaseRequestManager = QualityHoldReleaseRequestManager()

    class Meta:
        verbose_name = 'Quality hold release request'
        verbose_name_plural = 'Quality hold release requests'
        ordering = ['-created_at']

    def __str__(self) -> str:
        return (
            f'Release request for {self.products.count()}'
            f' products from {self.get_status_display()}'
        )

    @property
    def is_accepted(self) -> bool:
        return self.accepted_at is not None

    @property
    def is_processed(self) -> bool:
        return self.accepted_at is not None or self.rejected_at is not None

    def can_be_accepted(self) -> bool:
        return all(product.status == self.status for product in self.products.all())

    def accept(self, user: User) -> None:
        if self.is_processed:
            raise ValueError('Request has already been processed')

        if not self.can_be_accepted():
            raise ValueError(
                f'Cannot accept request - some products are no'
                f' longer in {self.get_status_display()} status'
            )

        self.accepted_at = timezone.now()
        self.accepted_by = user
        self.save()

        for product in self.products.all():
            if self.should_notify_logistic(product):
                email_data = {
                    'product_id': product.id,
                    'order_id': product.order.id,
                }
                for _, email_address in settings.LOGISTIC_RECIPIENTS:
                    send_html_mail(
                        template_name='mail_notify_logistic_quality_released.html',
                        subject='Products ready after Quality Blocker',
                        context=email_data,
                        to=email_address,
                    )
            if product.delivery_priority == DeliveryPriority.NOT_DELIVERED:
                product.priority_updater.change_delivery_priority(
                    DeliveryPriority.NORMAL
                )
            product.status_updater.change_status(
                status_to_change=ProductStatus.TO_BE_SHIPPED,
                owner=user,
            )

    def reject(self, user: User) -> None:
        if self.is_processed:
            raise ValueError('Request has already been processed')

        self.rejected_at = timezone.now()
        self.rejected_by = user
        self.save()

        self.products.update(quality_hold_release_request=None)

    @staticmethod
    def should_notify_logistic(product: Product) -> bool:
        return (
            product.status == ProductStatus.QUALITY_BLOCKER
            and product.order.status in {OrderStatus.SHIPPED, OrderStatus.DELIVERED}
        )
