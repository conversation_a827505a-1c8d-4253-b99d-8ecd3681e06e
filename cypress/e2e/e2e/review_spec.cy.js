import hp from "/pages/home_page"
import { isProdEnv } from "/test_modules/common_functions"

const mobileDevices = ['iphone-se2']
const desktopDevices = ['macbook-15']
const devices = [...mobileDevices, ...desktopDevices]

const name = 'name'
const testMail = '<EMAIL>'
const country = 'poland'
const review = 'review'
const file = 'cyTestImage.png'

beforeEach(() => {
    cy.intercept('POST', '/api/v2/reviews/').as('reviewSend')
})

checkAccessToReviewForm()
addBadReview()
devices.forEach((device) => {
    addGoodReview(device)         
})

function checkAccessToReviewForm() {
    devices.forEach((device) => {
        it(`User is able to reach review form on ${device}`, () => {
            cy.viewport(device)
            cy.visit('/')
            cy.agreeCookies()
            if (mobileDevices.includes(device)) {
                hp.openMegaMenuOnMobile()
            } else {
                hp.openMegaMenu()
            }
            cy.get(hp.megaMenu.selectors.top_menu.reviews).filter(':visible').click()
            cy.url().should('contain', '/review-list')
            cy.get('a.ty-btn-accent', { timeout:5000 }).filter(':visible').click()
            cy.url().should('contain', '/review-form')
        })
    })
}

function addGoodReview(device) {
    it(`User is able to fill the form and send GOOD review on device: ${device}`, () => {  
        cy.viewport(device)
        cy.visit('/de-de/review-form')
        cy.agreeCookies()
        cy.get('[data-testid="review-form-title"]').should('be.visible')
        cy.get('[data-testid="review-form-subtitle"]').should('be.visible')
        cy.get('[data-testid="review-form-description"]').should('be.visible')
        addUserData(name, country, testMail)
        selectFurnitureType(6)
        addStars(4)
        addFeedback('test')
        addReview(review)
        cy.get('[data-testid="review-form-photo-upload-title"]').should('be.visible')
        cy.get('[data-testid="review-form-photo-upload-showtime"]').should('be.visible')
        cy.get('[data-testid="review-form-photo-upload-input"]')
            .attachFile(file)
        cy.contains(`${file}`).should('be.visible')
        cy.get('[data-testid="review-form-remove-photo-button"]')
            .should('be.visible')
        if (!isProdEnv()) {
            submitReview()
            cy.wait('@reviewSend').then(({response}) => {
                const body = response.body
                expect(response.statusCode).to.eq(201)
                expect(body.name).to.equal(name)
                expect(body.email).to.equal(testMail)
                expect(body.country).to.equal(country)
                expect(body.score).to.equal(4)
                expect(body.photos).to.be.an('array').that.is.not.empty
            })
            cy.get('[data-testid="review-thanks-heading"]').should('be.visible')
            cy.get('[data-testid="review-thanks-name"]').should('be.visible').and('contain', name)
            cy.get('[data-testid="review-thanks-description"]').should('be.visible')
            cy.get('[data-testid="review-thanks-clipboard"]').should('be.visible')
            cy.get('[data-testid="review-thanks-review"]').should('be.visible')
            cy.get('[data-testid="review-thanks-social-link"]').should('be.visible').and('have.length', 3)
        }
    })
}

function addBadReview() {
    it('User is able to fill the form and send BAD review', () => {
        cy.visit('/de-de/review-form')
        cy.agreeCookies()
        addUserData(name, country, testMail)
        selectFurnitureType(6)
        addStars(3)
        addFeedback('test')
        addReview(review)
        if (!isProdEnv()) {
            submitReview()
            cy.wait('@reviewSend').then(({response}) => {
                const body = response.body
                expect(response.statusCode).to.eq(201)
                expect(response.body.name).to.equal(name)
                expect(response.body.email).to.equal(testMail)
                expect(response.body.country).to.equal(country)
                expect(response.body.score).to.equal(3)
                expect(response.body.photos).to.be.an('array').that.is.empty
            })
            cy.get('[data-testid="review-thanks-heading"]').should('be.visible')
            cy.get('[data-testid="review-thanks-name"]').should('be.visible').and('contain', name)
            cy.get('[data-testid="review-thanks-description"]').should('be.visible')
            cy.get('[data-testid="review-thanks-clipboard"]').should('not.exist')
            cy.get('[data-testid="review-thanks-review"]').should('not.exist')
            cy.get('[data-testid="review-thanks-social-link"]').should('not.exist')
            cy.get('[data-testid="review-thanks-social-image"]').should('not.exist')
        }
    })
}

function addStars(starsNum) {
    cy.get('[data-testid="review-form-star"]').eq(starsNum-1).click()
        cy.get('svg[data-testid="review-form-star"]')
            .filter((index, svg) => {
                return svg.getAttribute('style').includes('--active-star')
            }).should('have.length', starsNum)
        cy.get('svg[data-testid="review-form-star"]')
            .filter((index, svg) => {
                return svg.getAttribute('style').includes('--inactive-star')
            }).should('have.length', 5-starsNum)
}

function addFeedback(text) {
    cy.get('[data-testid="review-form-feedback-title"]').should('be.visible')
    cy.get('[data-testid="input-feedback"]').type(text)
}

function addReview(review) {
    cy.get('[data-testid="review-form-review-title"]').should('be.visible')
    cy.get('[data-testid="review-form-review-tips"]').should('be.visible')
    cy.get('[data-testid="review-form-review-input"]').type(review)
}

function submitReview() {
    cy.get('[data-testid="review-form-submit"]').click()
}

function addUserData(name, country, testMail) {
cy.get('[data-testid="input-first-name"]').should('be.visible')
    .type(name)
cy.get('select[id="input_2"]').select(country)
cy.get('[data-testid="input-email"]').should('be.visible')
    .type(testMail)
}

function  selectFurnitureType(index) {
    cy.get('[data-testid="review-form-furniture-type-selection"]').eq(index).click()
}
